# Extension Icons

This directory should contain the following icon files:
- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

You can create these icons using any image editor. The icons should represent the YouTube Lights Controller theme - perhaps a combination of a YouTube play button and a light bulb or home automation symbol.

For now, you can use placeholder icons or create simple colored squares:
- Use a red background with white play button symbol
- Or use a light bulb icon with YouTube red color scheme

The extension will work without these icons, but they improve the user experience.
