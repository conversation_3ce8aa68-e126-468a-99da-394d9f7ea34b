# Extension Icons

This directory contains vector SVG icons and conversion tools for the YouTube Lights Controller Chrome extension.

## Available Icons

### Main Icons (SVG + PNG)
- `icon16.svg/png` - 16x16 pixels (toolbar icon)
- `icon48.svg/png` - 48x48 pixels (extension management page)
- `icon128.svg/png` - 128x128 pixels (Chrome Web Store)
- `icon.svg/png` - Main icon (128px)

### Alternative Designs
- `icon-dark.svg` - Dark theme version
- `icon-minimal.svg` - Minimal flat design
- `icon-cinema.svg` - Cinema/film strip themed

## Design Elements

The icons combine:
- **YouTube red background** (#FF0000) with white play button
- **Light bulb** in gold/yellow (#FFD700) with radiating light rays
- **Connection indicator** showing the link between YouTube and lights
- **Gradients and shadows** for depth and visual appeal

## Converting SVG to PNG

### Automatic Conversion
Run the conversion script to generate PNG files:

**Windows:**
```bash
convert-icons.bat
```

**Linux/Mac:**
```bash
python3 convert-icons.py
```

### Manual Conversion
If you prefer to convert manually, you can:
1. Use online tools like [CloudConvert](https://cloudconvert.com/svg-to-png)
2. Use Inkscape: `inkscape --export-png=icon16.png --export-width=16 icon16.svg`
3. Use ImageMagick: `convert icon16.svg -resize 16x16 icon16.png`

## Requirements for Conversion Script

The Python conversion script requires:
```bash
pip install cairosvg pillow
```

## Using the Icons

The Chrome extension manifest.json is already configured to use the PNG versions:
```json
"icons": {
  "16": "icons/icon16.png",
  "48": "icons/icon48.png",
  "128": "icons/icon128.png"
}
```

## Customization

Feel free to modify the SVG files to match your preferences:
- Change colors by editing the `fill` and `stroke` attributes
- Adjust gradients in the `<defs>` section
- Modify the light bulb design or YouTube play button
- Add your own design elements

## Preview

Open `preview.html` in your browser to see all icon variations side by side.
