// YouTube Lights Controller - Content Script
class YouTubeLightsController {
  constructor() {
    this.isFullscreen = false;
    this.isCinemaMode = false;
    this.isPlaying = false;
    this.lastState = null;
    this.observer = null;
    this.init();
  }

  init() {
    console.log('YouTube Lights Controller initialized');
    this.setupObservers();
    this.checkInitialState();
    
    // Listen for fullscreen changes
    document.addEventListener('fullscreenchange', () => {
      this.checkFullscreenState();
    });
    
    // Listen for video play/pause events
    document.addEventListener('keydown', (e) => {
      if (e.code === 'Space' || e.code === 'KeyK') {
        setTimeout(() => this.checkPlayState(), 100);
      }
    });
  }

  setupObservers() {
    // Observer for DOM changes to detect cinema mode
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || mutation.attributeName === 'theater')) {
          this.checkCinemaMode();
        }
      });
    });

    // Start observing when the page is ready
    const startObserving = () => {
      const ytdApp = document.querySelector('ytd-app');
      if (ytdApp) {
        this.observer.observe(ytdApp, {
          attributes: true,
          attributeFilter: ['class', 'theater']
        });
      } else {
        setTimeout(startObserving, 1000);
      }
    };
    
    startObserving();
  }

  checkInitialState() {
    setTimeout(() => {
      this.checkCinemaMode();
      this.checkFullscreenState();
      this.checkPlayState();
    }, 2000);
  }

  checkCinemaMode() {
    const ytdApp = document.querySelector('ytd-app');
    const newCinemaMode = ytdApp && ytdApp.hasAttribute('theater');
    
    if (newCinemaMode !== this.isCinemaMode) {
      this.isCinemaMode = newCinemaMode;
      console.log('Cinema mode changed:', this.isCinemaMode);
      this.sendStateUpdate();
    }
  }

  checkFullscreenState() {
    const newFullscreen = !!document.fullscreenElement;
    
    if (newFullscreen !== this.isFullscreen) {
      this.isFullscreen = newFullscreen;
      console.log('Fullscreen changed:', this.isFullscreen);
      this.sendStateUpdate();
    }
  }

  checkPlayState() {
    const video = document.querySelector('video');
    const newPlaying = video && !video.paused;
    
    if (newPlaying !== this.isPlaying) {
      this.isPlaying = newPlaying;
      console.log('Play state changed:', this.isPlaying);
      this.sendStateUpdate();
    }
  }

  getCurrentState() {
    return {
      isFullscreen: this.isFullscreen,
      isCinemaMode: this.isCinemaMode,
      isPlaying: this.isPlaying,
      timestamp: Date.now(),
      url: window.location.href
    };
  }

  sendStateUpdate() {
    const currentState = this.getCurrentState();
    
    // Only send if state actually changed
    if (JSON.stringify(currentState) !== JSON.stringify(this.lastState)) {
      this.lastState = currentState;
      
      // Send to background script
      chrome.runtime.sendMessage({
        type: 'YOUTUBE_STATE_CHANGE',
        state: currentState
      });
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new YouTubeLightsController();
  });
} else {
  new YouTubeLightsController();
}
