// YouTube Lights Controller - Content Script
class YouTubeLightsController {
  constructor() {
    this.isFullscreen = false;
    this.isCinemaMode = false;
    this.isPlaying = false;
    this.lastState = null;
    this.observer = null;
    this.videoElement = null;
    this.checkInterval = null;
    this.init();
  }

  init() {
    console.log('🎬 YouTube Lights Controller - Content Script Loaded');
    console.log('Current URL:', window.location.href);

    // Wait for page to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    console.log('🎬 Setting up YouTube detection...');
    this.setupMessageListener();
    this.setupObservers();
    this.checkInitialState();

    // Listen for fullscreen changes
    document.addEventListener('fullscreenchange', () => {
      console.log('🎬 Fullscreen change detected');
      this.checkFullscreenState();
    });

    // Listen for video events directly
    this.setupVideoListeners();

    // Periodic check as backup
    this.checkInterval = setInterval(() => {
      this.checkAllStates();
    }, 2000);
  }

  setupMessageListener() {
    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'GET_STATE') {
        const currentState = this.getCurrentState();
        console.log('🎬 Popup requested state:', currentState);
        sendResponse({ state: currentState });
      }
    });
  }

  setupVideoListeners() {
    // Find and monitor video element
    const findVideo = () => {
      const video = document.querySelector('video');
      if (video && video !== this.videoElement) {
        console.log('🎬 Found video element:', video);
        this.videoElement = video;

        // Remove old listeners if any
        if (this.videoElement) {
          this.videoElement.removeEventListener('play', this.onVideoPlay);
          this.videoElement.removeEventListener('pause', this.onVideoPause);
        }

        // Add new listeners
        this.onVideoPlay = () => {
          console.log('🎬 Video play event');
          this.checkPlayState();
        };

        this.onVideoPause = () => {
          console.log('🎬 Video pause event');
          this.checkPlayState();
        };

        video.addEventListener('play', this.onVideoPlay);
        video.addEventListener('pause', this.onVideoPause);

        // Initial check
        this.checkPlayState();
      }
    };

    // Try to find video immediately and periodically
    findVideo();
    setInterval(findVideo, 3000);
  }

  setupObservers() {
    // Observer for DOM changes to detect cinema mode
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          this.checkCinemaMode();
        }
      });
    });

    // Start observing when the page is ready
    const startObserving = () => {
      const ytdApp = document.querySelector('ytd-app');
      if (ytdApp) {
        console.log('🎬 Started observing ytd-app for cinema mode changes');
        this.observer.observe(ytdApp, {
          attributes: true,
          attributeFilter: ['class', 'theater', 'masthead-hidden']
        });

        // Also observe the body for fullscreen classes
        this.observer.observe(document.body, {
          attributes: true,
          attributeFilter: ['class']
        });
      } else {
        console.log('🎬 ytd-app not found, retrying...');
        setTimeout(startObserving, 1000);
      }
    };

    startObserving();
  }

  checkInitialState() {
    console.log('🎬 Checking initial state...');
    setTimeout(() => {
      this.checkAllStates();
    }, 2000);
  }

  checkAllStates() {
    this.checkCinemaMode();
    this.checkFullscreenState();
    this.checkPlayState();
  }

  checkCinemaMode() {
    const ytdApp = document.querySelector('ytd-app');

    // Multiple ways to detect cinema mode
    let newCinemaMode = false;

    if (ytdApp) {
      // Method 1: theater attribute
      newCinemaMode = ytdApp.hasAttribute('theater');

      // Method 2: check for theater class
      if (!newCinemaMode) {
        newCinemaMode = ytdApp.classList.contains('theater') ||
                       ytdApp.classList.contains('theater-mode');
      }

      // Method 3: check masthead hidden (another indicator)
      if (!newCinemaMode) {
        newCinemaMode = ytdApp.hasAttribute('masthead-hidden');
      }
    }

    // Method 4: Check for theater mode in URL or page elements
    if (!newCinemaMode) {
      const theaterButton = document.querySelector('[data-tooltip-target-id="ytp-size-button"]');
      if (theaterButton) {
        newCinemaMode = theaterButton.getAttribute('aria-pressed') === 'true';
      }
    }

    if (newCinemaMode !== this.isCinemaMode) {
      this.isCinemaMode = newCinemaMode;
      console.log('🎬 Cinema mode changed:', this.isCinemaMode);
      this.sendStateUpdate();
    }
  }

  checkFullscreenState() {
    const newFullscreen = !!document.fullscreenElement ||
                         !!document.webkitFullscreenElement ||
                         !!document.mozFullScreenElement ||
                         !!document.msFullscreenElement;

    if (newFullscreen !== this.isFullscreen) {
      this.isFullscreen = newFullscreen;
      console.log('🎬 Fullscreen changed:', this.isFullscreen);
      this.sendStateUpdate();
    }
  }

  checkPlayState() {
    const video = document.querySelector('video');
    let newPlaying = false;

    if (video) {
      newPlaying = !video.paused && !video.ended && video.readyState > 2;
      console.log('🎬 Video state - paused:', video.paused, 'ended:', video.ended, 'readyState:', video.readyState);
    } else {
      console.log('🎬 No video element found');
    }

    if (newPlaying !== this.isPlaying) {
      this.isPlaying = newPlaying;
      console.log('🎬 Play state changed:', this.isPlaying);
      this.sendStateUpdate();
    }
  }

  getCurrentState() {
    return {
      isFullscreen: this.isFullscreen,
      isCinemaMode: this.isCinemaMode,
      isPlaying: this.isPlaying,
      timestamp: Date.now(),
      url: window.location.href
    };
  }

  sendStateUpdate() {
    const currentState = this.getCurrentState();

    // Only send if state actually changed
    if (JSON.stringify(currentState) !== JSON.stringify(this.lastState)) {
      this.lastState = currentState;

      console.log('🎬 Sending state update:', currentState);

      // Send to background script
      chrome.runtime.sendMessage({
        type: 'YOUTUBE_STATE_CHANGE',
        state: currentState
      }).catch(error => {
        console.error('🎬 Error sending message to background:', error);
      });
    }
  }

  // Cleanup method
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
    if (this.videoElement) {
      this.videoElement.removeEventListener('play', this.onVideoPlay);
      this.videoElement.removeEventListener('pause', this.onVideoPause);
    }
  }
}

// Initialize the controller
console.log('🎬 YouTube Lights Controller - Script Starting');

// Only run on YouTube watch pages
if (window.location.hostname === 'www.youtube.com' &&
    window.location.pathname === '/watch') {

  console.log('🎬 On YouTube watch page, initializing controller');

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.youtubeLightsController = new YouTubeLightsController();
    });
  } else {
    window.youtubeLightsController = new YouTubeLightsController();
  }

  // Handle navigation within YouTube (SPA)
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      console.log('🎬 YouTube navigation detected:', url);

      // Cleanup old controller
      if (window.youtubeLightsController) {
        window.youtubeLightsController.destroy();
      }

      // Initialize new controller if on watch page
      if (url.includes('/watch')) {
        setTimeout(() => {
          window.youtubeLightsController = new YouTubeLightsController();
        }, 1000);
      }
    }
  }).observe(document, { subtree: true, childList: true });

} else {
  console.log('🎬 Not on YouTube watch page, skipping initialization');
}
