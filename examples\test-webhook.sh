#!/bin/bash

# Test script for YouTube Lights Controller webhook
# Usage: ./test-webhook.sh [your-ha-url] [webhook-id]

# Default values (change these to match your setup)
HA_URL="${1:-http://homeassistant.local:8123}"
WEBHOOK_ID="${2:-youtube_lights_webhook}"

echo "Testing YouTube Lights Controller webhook..."
echo "Home Assistant URL: $HA_URL"
echo "Webhook ID: $WEBHOOK_ID"
echo ""

# Test 1: Cinema mode playing
echo "Test 1: Cinema mode playing"
curl -X POST \
  "$HA_URL/api/webhook/$WEBHOOK_ID" \
  -H 'Content-Type: application/json' \
  -d '{
    "youtube_state": {
      "isFullscreen": false,
      "isCinemaMode": true,
      "isPlaying": true,
      "timestamp": '$(date +%s000)',
      "url": "https://youtube.com/watch?v=test1"
    },
    "source": "test_script"
  }'
echo -e "\n"

sleep 3

# Test 2: Fullscreen playing
echo "Test 2: Fullscreen playing"
curl -X POST \
  "$HA_URL/api/webhook/$WEBHOOK_ID" \
  -H 'Content-Type: application/json' \
  -d '{
    "youtube_state": {
      "isFullscreen": true,
      "isCinemaMode": false,
      "isPlaying": true,
      "timestamp": '$(date +%s000)',
      "url": "https://youtube.com/watch?v=test2"
    },
    "source": "test_script"
  }'
echo -e "\n"

sleep 3

# Test 3: Paused
echo "Test 3: Video paused"
curl -X POST \
  "$HA_URL/api/webhook/$WEBHOOK_ID" \
  -H 'Content-Type: application/json' \
  -d '{
    "youtube_state": {
      "isFullscreen": true,
      "isCinemaMode": false,
      "isPlaying": false,
      "timestamp": '$(date +%s000)',
      "url": "https://youtube.com/watch?v=test2"
    },
    "source": "test_script"
  }'
echo -e "\n"

sleep 3

# Test 4: Back to idle
echo "Test 4: Back to idle"
curl -X POST \
  "$HA_URL/api/webhook/$WEBHOOK_ID" \
  -H 'Content-Type: application/json' \
  -d '{
    "youtube_state": {
      "isFullscreen": false,
      "isCinemaMode": false,
      "isPlaying": false,
      "timestamp": '$(date +%s000)',
      "url": null
    },
    "source": "test_script"
  }'
echo -e "\n"

echo "Test completed! Check your Home Assistant sensor.youtube_lights_state for updates."
