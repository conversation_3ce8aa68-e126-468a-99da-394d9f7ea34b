@echo off
echo Converting SVG icons to PNG format...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import cairosvg, PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install cairosvg pillow
    if errorlevel 1 (
        echo Error: Failed to install required packages
        pause
        exit /b 1
    )
)

REM Run the conversion script
python convert-icons.py

echo.
echo Conversion complete! You can now use the PNG files in your Chrome extension.
pause
