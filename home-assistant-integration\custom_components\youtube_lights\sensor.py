"""Sensor platform for YouTube Lights Controller."""
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from homeassistant.components.sensor import SensorEntity
from homeassistant.config_entries import ConfigEntry
from homeassistant.core import HomeAssistant, callback
from homeassistant.helpers.entity_platform import AddEntitiesCallback
from homeassistant.helpers.restore_state import RestoreEntity

from .const import (
    DOMAIN,
    ATTR_IS_FULLSCREEN,
    ATTR_IS_CINEMA_MODE,
    ATTR_IS_PLAYING,
    ATTR_LAST_UPDATE,
    ATTR_URL,
    YOUTUBE_STATE_IDLE,
    YOUTUBE_STATE_PLAYING,
    YOUTUBE_STATE_CINEMA_PLAYING,
    YOUTUBE_STATE_CINEMA_PAUSED,
    YOUTUBE_STATE_FULLSCREEN_PLAYING,
    YOUTUBE_STATE_FULLSCREEN_PAUSED,
)

_LOGGER = logging.getLogger(__name__)


async def async_setup_entry(
    hass: HomeAssistant,
    config_entry: ConfigEntry,
    async_add_entities: AddEntitiesCallback,
) -> None:
    """Set up the YouTube Lights Controller sensor."""
    _LOGGER.info("Setting up YouTube Lights Controller sensor")
    
    sensor = YouTubeLightsSensor(hass, config_entry)
    async_add_entities([sensor], True)


class YouTubeLightsSensor(SensorEntity, RestoreEntity):
    """Representation of a YouTube Lights Controller sensor."""

    def __init__(self, hass: HomeAssistant, config_entry: ConfigEntry) -> None:
        """Initialize the sensor."""
        self.hass = hass
        self.config_entry = config_entry
        self._attr_name = "YouTube Lights State"
        self._attr_unique_id = f"{DOMAIN}_state"
        self._attr_icon = "mdi:youtube"
        
        # Initialize state
        self._state = YOUTUBE_STATE_IDLE
        self._is_fullscreen = False
        self._is_cinema_mode = False
        self._is_playing = False
        self._last_update = None
        self._url = None
        
        # Register webhook if configured
        webhook_id = config_entry.data.get("webhook_id")
        if webhook_id:
            self._register_webhook(webhook_id)

    async def async_added_to_hass(self) -> None:
        """When entity is added to hass."""
        await super().async_added_to_hass()
        
        # Restore previous state
        last_state = await self.async_get_last_state()
        if last_state is not None:
            self._state = last_state.state
            if last_state.attributes:
                self._is_fullscreen = last_state.attributes.get(ATTR_IS_FULLSCREEN, False)
                self._is_cinema_mode = last_state.attributes.get(ATTR_IS_CINEMA_MODE, False)
                self._is_playing = last_state.attributes.get(ATTR_IS_PLAYING, False)
                self._last_update = last_state.attributes.get(ATTR_LAST_UPDATE)
                self._url = last_state.attributes.get(ATTR_URL)

    def _register_webhook(self, webhook_id: str) -> None:
        """Register webhook for receiving YouTube state updates."""
        _LOGGER.info(f"Registering webhook: {webhook_id}")
        
        @callback
        async def handle_webhook(hass, webhook_id, request):
            """Handle incoming webhook data."""
            try:
                data = await request.json()
                _LOGGER.debug(f"Received webhook data: {data}")
                
                if "youtube_state" in data:
                    await self.update_youtube_state(data["youtube_state"])
                    
            except Exception as e:
                _LOGGER.error(f"Error processing webhook data: {e}")
                return self.json_message("Error processing data", 400)
            
            return self.json_message("OK")
        
        self.hass.components.webhook.async_register(
            DOMAIN, "YouTube Lights Controller", webhook_id, handle_webhook
        )

    async def update_youtube_state(self, state_data: Dict[str, Any]) -> None:
        """Update the sensor state based on YouTube data."""
        try:
            self._is_fullscreen = state_data.get("isFullscreen", False)
            self._is_cinema_mode = state_data.get("isCinemaMode", False)
            self._is_playing = state_data.get("isPlaying", False)
            self._last_update = datetime.now().isoformat()
            self._url = state_data.get("url")
            
            # Determine state string
            if self._is_fullscreen and self._is_playing:
                self._state = YOUTUBE_STATE_FULLSCREEN_PLAYING
            elif self._is_fullscreen:
                self._state = YOUTUBE_STATE_FULLSCREEN_PAUSED
            elif self._is_cinema_mode and self._is_playing:
                self._state = YOUTUBE_STATE_CINEMA_PLAYING
            elif self._is_cinema_mode:
                self._state = YOUTUBE_STATE_CINEMA_PAUSED
            elif self._is_playing:
                self._state = YOUTUBE_STATE_PLAYING
            else:
                self._state = YOUTUBE_STATE_IDLE
            
            _LOGGER.info(f"Updated YouTube state: {self._state}")
            
            # Trigger state update
            self.async_write_ha_state()
            
            # Fire event for automations
            self.hass.bus.async_fire(
                "youtube_lights_state_changed",
                {
                    "state": self._state,
                    "is_fullscreen": self._is_fullscreen,
                    "is_cinema_mode": self._is_cinema_mode,
                    "is_playing": self._is_playing,
                    "url": self._url,
                }
            )
            
            # Trigger light control if configured
            await self._control_lights()
            
        except Exception as e:
            _LOGGER.error(f"Error updating YouTube state: {e}")

    async def _control_lights(self) -> None:
        """Control lights based on current state."""
        try:
            light_entities = self.config_entry.data.get("light_entities", [])
            if not light_entities:
                return
            
            # Get brightness settings
            brightness_cinema = self.config_entry.data.get("brightness_cinema", 30)
            brightness_fullscreen = self.config_entry.data.get("brightness_fullscreen", 10)
            brightness_normal = self.config_entry.data.get("brightness_normal", 100)
            transition_time = self.config_entry.data.get("transition_time", 2)
            
            # Determine target brightness
            if self._is_fullscreen:
                target_brightness = brightness_fullscreen
            elif self._is_cinema_mode:
                target_brightness = brightness_cinema
            else:
                target_brightness = brightness_normal
            
            # Convert percentage to 0-255 range
            brightness_value = int((target_brightness / 100) * 255)
            
            # Control each light
            for entity_id in light_entities:
                if self._is_fullscreen or self._is_cinema_mode:
                    # Dim lights
                    await self.hass.services.async_call(
                        "light",
                        "turn_on",
                        {
                            "entity_id": entity_id,
                            "brightness": brightness_value,
                            "transition": transition_time,
                        },
                    )
                else:
                    # Return to normal brightness
                    await self.hass.services.async_call(
                        "light",
                        "turn_on",
                        {
                            "entity_id": entity_id,
                            "brightness": brightness_value,
                            "transition": transition_time,
                        },
                    )
            
            _LOGGER.info(f"Controlled {len(light_entities)} lights with brightness {target_brightness}%")
            
        except Exception as e:
            _LOGGER.error(f"Error controlling lights: {e}")

    @property
    def state(self) -> str:
        """Return the state of the sensor."""
        return self._state

    @property
    def extra_state_attributes(self) -> Dict[str, Any]:
        """Return the state attributes."""
        return {
            ATTR_IS_FULLSCREEN: self._is_fullscreen,
            ATTR_IS_CINEMA_MODE: self._is_cinema_mode,
            ATTR_IS_PLAYING: self._is_playing,
            ATTR_LAST_UPDATE: self._last_update,
            ATTR_URL: self._url,
        }

    @property
    def device_info(self) -> Dict[str, Any]:
        """Return device information."""
        return {
            "identifiers": {(DOMAIN, "youtube_lights_controller")},
            "name": "YouTube Lights Controller",
            "manufacturer": "Custom Integration",
            "model": "YouTube Lights Controller",
            "sw_version": "1.0.0",
        }
