<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Dark theme version -->
  <defs>
    <radialGradient id="darkRedGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#CC0000"/>
      <stop offset="100%" stop-color="#880000"/>
    </radialGradient>
    <radialGradient id="darkLightGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#FFD700"/>
      <stop offset="100%" stop-color="#FFA500"/>
    </radialGradient>
  </defs>
  
  <!-- Dark background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#darkRedGradient)" stroke="#660000" stroke-width="2"/>
  
  <!-- YouTube play button -->
  <polygon points="50,45 50,83 85,64" fill="#F0F0F0"/>
  
  <!-- Light bulb -->
  <g transform="translate(85, 25)">
    <ellipse cx="15" cy="20" rx="12" ry="16" fill="url(#darkLightGradient)" stroke="#CC8800" stroke-width="1.5"/>
    <rect x="9" y="32" width="12" height="8" rx="2" fill="#333333" stroke="#555555" stroke-width="1"/>
    
    <!-- Light rays -->
    <g stroke="#FFD700" stroke-width="2.5" stroke-linecap="round" opacity="0.9">
      <line x1="15" y1="5" x2="15" y2="1"/>
      <line x1="27" y1="12" x2="31" y2="8"/>
      <line x1="31" y1="20" x2="35" y2="20"/>
      <line x1="27" y1="28" x2="31" y2="32"/>
      <line x1="3" y1="12" x2="-1" y2="8"/>
      <line x1="-1" y1="20" x2="-5" y2="20"/>
      <line x1="3" y1="28" x2="-1" y2="32"/>
    </g>
  </g>
  
  <!-- Connection line -->
  <path d="M 75 64 Q 85 50 95 40" stroke="#FFD700" stroke-width="2.5" fill="none" stroke-dasharray="4,2"/>
</svg>
