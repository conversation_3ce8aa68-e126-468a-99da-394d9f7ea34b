{"config": {"step": {"user": {"title": "YouTube Lights Controller Setup", "description": "Configure the YouTube Lights Controller integration.\n\nWebhook URL will be: {webhook_url}", "data": {"webhook_id": "Webhook ID (optional)", "light_entities": "Light entities to control", "brightness_cinema": "Cinema mode brightness (%)", "brightness_fullscreen": "Fullscreen brightness (%)", "brightness_normal": "Normal brightness (%)", "transition_time": "Transition time (seconds)"}}}, "error": {"invalid_webhook_id": "Webhook ID can only contain letters, numbers, underscores, and hyphens"}}, "options": {"step": {"init": {"title": "YouTube Lights Controller Options", "description": "Update your YouTube Lights Controller settings", "data": {"webhook_id": "Webhook ID (optional)", "light_entities": "Light entities to control", "brightness_cinema": "Cinema mode brightness (%)", "brightness_fullscreen": "Fullscreen brightness (%)", "brightness_normal": "Normal brightness (%)", "transition_time": "Transition time (seconds)"}}}, "error": {"invalid_webhook_id": "Webhook ID can only contain letters, numbers, underscores, and hyphens"}}}