#!/usr/bin/env python3
"""
Verify PNG icons and display their properties.
"""

from PIL import Image
from pathlib import Path
import os

def verify_png(png_path):
    """Verify a PNG file and return its properties."""
    try:
        with Image.open(png_path) as img:
            return {
                'size': img.size,
                'mode': img.mode,
                'format': img.format,
                'file_size': os.path.getsize(png_path)
            }
    except Exception as e:
        return {'error': str(e)}

def format_file_size(size_bytes):
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"

def main():
    """Verify all PNG icons."""
    icons_dir = Path(__file__).parent
    
    png_files = [
        'icon16.png',
        'icon48.png', 
        'icon128.png',
        'icon.png'
    ]
    
    print("🔍 Verifying PNG Icons")
    print("=" * 50)
    
    total_size = 0
    valid_count = 0
    
    for png_file in png_files:
        png_path = icons_dir / png_file
        
        if png_path.exists():
            props = verify_png(png_path)
            
            if 'error' in props:
                print(f"❌ {png_file}: ERROR - {props['error']}")
            else:
                file_size = props['file_size']
                total_size += file_size
                valid_count += 1
                
                print(f"✅ {png_file}")
                print(f"   Size: {props['size'][0]}x{props['size'][1]} pixels")
                print(f"   Mode: {props['mode']} ({props['format']})")
                print(f"   File: {format_file_size(file_size)}")
                print()
        else:
            print(f"❌ {png_file}: File not found")
    
    print("=" * 50)
    print(f"Summary: {valid_count}/{len(png_files)} icons verified")
    print(f"Total size: {format_file_size(total_size)}")
    
    if valid_count == len(png_files):
        print("🎉 All PNG icons are ready for Chrome extension!")
        print("\nNext steps:")
        print("1. Load the extension in Chrome (chrome://extensions/)")
        print("2. Enable Developer mode")
        print("3. Click 'Load unpacked' and select the chrome-extension folder")
    else:
        print("⚠️  Some icons are missing or invalid")

if __name__ == "__main__":
    main()
