"""YouTube Lights Controller integration for Home Assistant."""
import logging
from datetime import <PERSON><PERSON><PERSON>

import voluptuous as vol
from homeassistant.config_entries import ConfigEntry
from homeassistant.const import Platform
from homeassistant.core import HomeAssistant
from homeassistant.helpers import config_validation as cv
from homeassistant.helpers.typing import ConfigType

from .const import DOMAIN, CONF_WEBHOOK_ID, CONF_LIGHT_ENTITIES, CONF_SENSOR_ENTITIES

_LOGGER = logging.getLogger(__name__)

PLATFORMS = [Platform.SENSOR, Platform.AUTOMATION]

CONFIG_SCHEMA = vol.Schema(
    {
        DOMAIN: vol.Schema(
            {
                vol.Optional(CONF_WEBHOOK_ID): cv.string,
                vol.Optional(CONF_LIGHT_ENTITIES, default=[]): cv.ensure_list,
                vol.Optional(CONF_SENSOR_ENTITIES, default=[]): cv.ensure_list,
            }
        )
    },
    extra=vol.ALLOW_EXTRA,
)


async def async_setup(hass: HomeAssistant, config: ConfigType) -> bool:
    """Set up the YouTube Lights Controller integration."""
    _LOGGER.info("Setting up YouTube Lights Controller integration")
    
    hass.data.setdefault(DOMAIN, {})
    
    # Store configuration
    if DOMAIN in config:
        hass.data[DOMAIN]["config"] = config[DOMAIN]
    else:
        hass.data[DOMAIN]["config"] = {}
    
    # Initialize state storage
    hass.data[DOMAIN]["state"] = {
        "is_fullscreen": False,
        "is_cinema_mode": False,
        "is_playing": False,
        "last_update": None,
        "url": None
    }
    
    return True


async def async_setup_entry(hass: HomeAssistant, entry: ConfigEntry) -> bool:
    """Set up YouTube Lights Controller from a config entry."""
    _LOGGER.info("Setting up YouTube Lights Controller config entry")
    
    hass.data.setdefault(DOMAIN, {})
    hass.data[DOMAIN][entry.entry_id] = entry.data
    
    # Forward the setup to the sensor platform
    await hass.config_entries.async_forward_entry_setups(entry, PLATFORMS)
    
    return True


async def async_unload_entry(hass: HomeAssistant, entry: ConfigEntry) -> bool:
    """Unload a config entry."""
    _LOGGER.info("Unloading YouTube Lights Controller config entry")
    
    unload_ok = await hass.config_entries.async_unload_platforms(entry, PLATFORMS)
    
    if unload_ok:
        hass.data[DOMAIN].pop(entry.entry_id)
    
    return unload_ok
