# YouTube Lights Controller - Chrome Extension

This Chrome extension detects YouTube viewing states and communicates with Home Assistant to control smart lights.

## Installation

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked" and select this folder
4. The extension should now appear in your extensions list

## Configuration

1. Click the extension icon in Chrome toolbar
2. Enter your Home Assistant details:
   - **Home Assistant URL**: Your HA instance URL (e.g., `http://homeassistant.local:8123`)
   - **Access Token**: Long-lived access token from HA (Profile → Security → Long-Lived Access Tokens)
   - **Webhook ID**: Optional webhook ID for webhook-based communication
3. Click "Save Settings"
4. Click "Test Connection" to verify setup

## Features

- **YouTube State Detection**: Automatically detects:
  - Cinema mode (theater mode)
  - Fullscreen mode
  - Video playing/paused state
  
- **Real-time Communication**: Sends state changes to Home Assistant via:
  - REST API (using access token)
  - Webhooks (if webhook ID configured)

- **Visual Feedback**: Popup shows current YouTube state and connection status

## How It Works

1. **Content Script** (`content.js`): Monitors YouTube pages for DOM changes and state transitions
2. **Background Script** (`background.js`): Handles communication with Home Assistant
3. **Popup Interface** (`popup.html/js`): Provides configuration and status interface

## Detected States

- `idle`: No video playing
- `playing`: Video playing in normal mode
- `cinema_playing`: Video playing in cinema mode
- `cinema_paused`: Video paused in cinema mode
- `fullscreen_playing`: Video playing in fullscreen
- `fullscreen_paused`: Video paused in fullscreen

## Permissions

The extension requires these permissions:
- `activeTab`: To interact with YouTube pages
- `storage`: To save configuration settings
- `tabs`: To detect YouTube navigation
- Host permissions for YouTube and your Home Assistant instance

## Troubleshooting

### Extension Not Working
- Refresh YouTube page after installing
- Check that you're on a YouTube video page (`youtube.com/watch`)
- Open browser console (F12) and check for errors

### Connection Issues
- Verify Home Assistant URL is correct and accessible from your browser
- Ensure access token is valid and has necessary permissions
- Check that Home Assistant is running and reachable
- Test the connection using the "Test Connection" button

### State Detection Issues
- YouTube's DOM structure may change; the extension monitors for common selectors
- Try switching between cinema mode and normal mode manually
- Check browser console for any JavaScript errors

## Development

### File Structure
```
chrome-extension/
├── manifest.json          # Extension manifest
├── content.js            # YouTube page monitoring
├── background.js         # HA communication
├── popup.html           # Configuration UI
├── popup.js             # Popup logic
└── icons/               # Extension icons
```

### Testing
1. Load the extension in developer mode
2. Open YouTube and play a video
3. Toggle cinema mode and fullscreen
4. Check the popup for state updates
5. Verify Home Assistant receives the updates

### Debugging
- Open Chrome DevTools on YouTube page to see content script logs
- Go to `chrome://extensions/` and click "background page" to see background script logs
- Right-click extension popup and select "Inspect" for popup debugging

## API Communication

### REST API Format
```javascript
POST /api/states/sensor.youtube_lights_state
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "state": "cinema_playing",
  "attributes": {
    "isFullscreen": false,
    "isCinemaMode": true,
    "isPlaying": true,
    "timestamp": 1234567890,
    "url": "https://youtube.com/watch?v=...",
    "friendly_name": "YouTube Lights State",
    "icon": "mdi:youtube"
  }
}
```

### Webhook Format
```javascript
POST /api/webhook/YOUR_WEBHOOK_ID
Content-Type: application/json

{
  "youtube_state": {
    "isFullscreen": false,
    "isCinemaMode": true,
    "isPlaying": true,
    "timestamp": 1234567890,
    "url": "https://youtube.com/watch?v=..."
  },
  "source": "youtube_lights_extension"
}
```
