# Quick Installation Guide

## Prerequisites
- Google Chrome browser
- Home Assistant instance (local or remote)
- Smart lights compatible with Home Assistant

## Step 1: Install Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top-right corner)
3. Click "Load unpacked" and select the `chrome-extension` folder
4. The YouTube Lights Controller extension should now appear in your extensions list

## Step 2: Install Home Assistant Integration

### Option A: Manual Installation
1. Copy the entire `home-assistant-integration/custom_components/youtube_lights` folder to your Home Assistant `custom_components` directory:
   ```
   /config/custom_components/youtube_lights/
   ```
2. Restart Home Assistant

### Option B: HACS (if you publish to HACS later)
1. Add this repository to HACS as a custom repository
2. Install "YouTube Lights Controller" from HACS
3. Restart Home Assistant

## Step 3: Configure Home Assistant Integration

1. Go to Settings → Devices & Services → Add Integration
2. Search for "YouTube Lights Controller"
3. Configure the integration:
   - **Webhook ID**: Choose a unique ID (e.g., `youtube_lights_webhook`)
   - **Light Entities**: Select which lights to control automatically
   - **Brightness Levels**: 
     - Cinema Mode: 30% (recommended)
     - Fullscreen: 10% (recommended)
     - Normal: 100% (recommended)
   - **Transition Time**: 2 seconds (recommended)

## Step 4: Configure Chrome Extension

1. Click the YouTube Lights Controller extension icon in Chrome
2. Enter your Home Assistant details:
   - **Home Assistant URL**: Your HA URL (e.g., `http://homeassistant.local:8123`)
   - **Access Token**: Create a Long-Lived Access Token in HA:
     - Go to Profile → Security → Long-Lived Access Tokens
     - Click "Create Token" and copy it
   - **Webhook ID**: Enter the same webhook ID you used in Step 3
3. Click "Save Settings"
4. Click "Test Connection" to verify everything works

## Step 5: Test the Setup

1. Open YouTube in Chrome
2. Play any video
3. Toggle cinema mode (theater mode button)
4. Toggle fullscreen mode
5. Watch your lights automatically adjust!

## Verification

- Check the extension popup to see current YouTube state
- Check Home Assistant → Developer Tools → States → `sensor.youtube_lights_state`
- Your configured lights should dim/brighten based on YouTube state

## Troubleshooting

If something isn't working:

1. **Extension Issues**: Check browser console (F12) for errors
2. **Connection Issues**: Verify HA URL and access token
3. **Light Control Issues**: Check that light entities are correct and responsive
4. **Webhook Issues**: Use the test scripts in the `examples/` folder

## Next Steps

- Explore the example automations in `examples/home-assistant-automations.yaml`
- Customize brightness levels and transition times
- Add more complex automations based on your needs
- Consider adding motion sensors for smarter automation

Enjoy your automated YouTube lighting experience! 🎬💡
