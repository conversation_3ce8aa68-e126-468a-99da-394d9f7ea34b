# YouTube Lights Controller - Home Assistant Integration

A custom Home Assistant integration that receives YouTube viewing state from the Chrome extension and automatically controls smart lights.

## Installation

### HACS (Recommended)
1. Add this repository to HACS as a custom repository
2. Install "YouTube Lights Controller" from HACS
3. Restart Home Assistant

### Manual Installation
1. Copy the `custom_components/youtube_lights` folder to your Home Assistant `custom_components` directory:
   ```
   /config/custom_components/youtube_lights/
   ```
2. Restart Home Assistant

## Setup

1. Go to Settings → Devices & Services
2. Click "Add Integration"
3. Search for "YouTube Lights Controller"
4. Follow the configuration wizard:
   - **Webhook ID**: Optional, must match Chrome extension setting
   - **Light Entities**: Select lights to control automatically
   - **Brightness Settings**: Configure brightness levels for different modes
   - **Transition Time**: How long brightness changes take

## Configuration Options

### Brightness Levels
- **Cinema Mode**: Default 30% - Warm, dimmed lighting for comfortable viewing
- **Fullscreen**: Default 10% - Minimal lighting to reduce distractions
- **Normal**: Default 100% - Full brightness when not in special modes

### Transition Time
- Default 2 seconds - How long it takes for lights to change brightness
- Range: 0-60 seconds

## Entities Created

### Sensor: `sensor.youtube_lights_state`
Tracks the current YouTube viewing state with these possible values:
- `idle`: No video playing
- `playing`: Video playing normally
- `cinema_playing`: Video playing in cinema mode
- `cinema_paused`: Video paused in cinema mode
- `fullscreen_playing`: Video playing in fullscreen
- `fullscreen_paused`: Video paused in fullscreen

#### Attributes
- `is_fullscreen`: Boolean indicating fullscreen state
- `is_cinema_mode`: Boolean indicating cinema mode state
- `is_playing`: Boolean indicating if video is playing
- `last_update`: Timestamp of last state change
- `url`: Current YouTube video URL

## Services

### `youtube_lights.update_state`
Manually update the YouTube state (useful for testing)

```yaml
service: youtube_lights.update_state
data:
  is_fullscreen: true
  is_cinema_mode: false
  is_playing: true
  url: "https://youtube.com/watch?v=example"
```

### `youtube_lights.control_lights`
Manually trigger light control based on a specific state

```yaml
service: youtube_lights.control_lights
data:
  state: "fullscreen_playing"
  light_entities:  # Optional, uses config if not specified
    - light.living_room
    - light.tv_backlight
```

## Events

### `youtube_lights_state_changed`
Fired whenever the YouTube state changes, useful for custom automations

```yaml
automation:
  - alias: "Custom YouTube Action"
    trigger:
      - platform: event
        event_type: youtube_lights_state_changed
    condition:
      - condition: template
        value_template: "{{ trigger.event.data.state == 'fullscreen_playing' }}"
    action:
      - service: notify.mobile_app
        data:
          message: "YouTube entered fullscreen mode"
```

## Automation Examples

### Basic Light Control
The integration automatically controls configured lights, but you can create additional automations:

```yaml
# Turn off all other lights when entering fullscreen
automation:
  - alias: "YouTube Fullscreen - Lights Off"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        to: "fullscreen_playing"
    action:
      - service: light.turn_off
        target:
          area_id: kitchen
        data:
          transition: 3

# Restore lights when exiting YouTube
  - alias: "YouTube Stopped - Restore Lights"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        to: "idle"
        for: "00:01:00"  # Wait 1 minute to avoid flickering
    action:
      - service: scene.turn_on
        target:
          entity_id: scene.evening_lights
```

### Advanced Automations

```yaml
# Different behavior based on time of day
automation:
  - alias: "YouTube Cinema Mode - Time Based"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        to: "cinema_playing"
    action:
      - choose:
          - conditions:
              - condition: time
                after: "22:00:00"
                before: "06:00:00"
            sequence:
              - service: light.turn_on
                target:
                  entity_id: light.tv_backlight
                data:
                  brightness: 50
                  color_name: red
          - conditions:
              - condition: time
                after: "06:00:00"
                before: "22:00:00"
            sequence:
              - service: light.turn_on
                target:
                  entity_id: light.living_room
                data:
                  brightness: 100
                  color_temp: 400

# Pause detection with different actions
  - alias: "YouTube Paused - Gradual Restore"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        from: "cinema_playing"
        to: "cinema_paused"
        for: "00:00:30"  # Wait 30 seconds
    action:
      - service: light.turn_on
        target:
          entity_id: light.living_room
        data:
          brightness_pct: 60
          transition: 5
```

## Communication Methods

### Webhook (Recommended)
- Configure a webhook ID in both the Chrome extension and HA integration
- More reliable and faster than API calls
- Webhook URL format: `http://your-ha-instance:8123/api/webhook/YOUR_WEBHOOK_ID`

### REST API
- Uses Home Assistant's REST API with long-lived access token
- Fallback method if webhooks aren't working
- Creates/updates the sensor entity directly

## Troubleshooting

### Integration Not Loading
- Check Home Assistant logs for errors during startup
- Ensure all files are in the correct directory structure
- Restart Home Assistant after installation

### No State Updates
- Verify Chrome extension is configured correctly
- Check webhook ID matches between extension and integration
- Test webhook manually using curl or Postman
- Review Home Assistant logs for webhook errors

### Lights Not Responding
- Verify light entities are available and responsive
- Check entity IDs in integration configuration
- Test light control manually using Developer Tools
- Review automation traces for errors

### Webhook Issues
```bash
# Test webhook manually
curl -X POST \
  http://your-ha-instance:8123/api/webhook/your_webhook_id \
  -H 'Content-Type: application/json' \
  -d '{
    "youtube_state": {
      "isFullscreen": true,
      "isCinemaMode": false,
      "isPlaying": true,
      "timestamp": 1234567890,
      "url": "https://youtube.com/watch?v=test"
    },
    "source": "manual_test"
  }'
```

## Development

### File Structure
```
custom_components/youtube_lights/
├── __init__.py          # Integration setup
├── config_flow.py       # Configuration UI
├── const.py            # Constants and defaults
├── manifest.json       # Integration metadata
├── sensor.py           # Sensor platform
├── services.yaml       # Service definitions
└── strings.json        # UI translations
```

### Adding Features
1. Update constants in `const.py`
2. Modify sensor logic in `sensor.py`
3. Add new services in `services.yaml`
4. Update configuration flow if needed
5. Test thoroughly with the Chrome extension

### Debugging
- Enable debug logging in `configuration.yaml`:
  ```yaml
  logger:
    logs:
      custom_components.youtube_lights: debug
  ```
- Check Home Assistant logs for detailed information
- Use Developer Tools to test services and inspect entity states
