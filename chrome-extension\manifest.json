{"manifest_version": 3, "name": "YouTube Lights Controller", "version": "1.0.0", "description": "Controls Home Assistant lights based on YouTube viewing state", "permissions": ["activeTab", "storage", "tabs"], "host_permissions": ["https://www.youtube.com/*", "http://localhost:8123/*", "https://*.duckdns.org/*", "https://*.nabu.casa/*"], "content_scripts": [{"matches": ["https://www.youtube.com/*"], "js": ["content.js"], "run_at": "document_end"}], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "YouTube Lights Controller"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}