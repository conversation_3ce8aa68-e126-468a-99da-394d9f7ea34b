<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Minimal flat design version -->
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="58" fill="#FF0000"/>
  
  <!-- YouTube play button -->
  <polygon points="52,48 52,80 80,64" fill="white"/>
  
  <!-- Simple light bulb icon -->
  <g transform="translate(88, 28)">
    <!-- Bulb outline -->
    <path d="M 12 8 C 18 8 22 12 22 18 C 22 22 20 25 18 27 L 18 32 L 6 32 L 6 27 C 4 25 2 22 2 18 C 2 12 6 8 12 8 Z" fill="#FFD700"/>
    <!-- Base -->
    <rect x="6" y="32" width="12" height="6" fill="#E0E0E0"/>
    <!-- Simple rays -->
    <circle cx="12" cy="4" r="1" fill="#FFD700"/>
    <circle cx="26" cy="18" r="1" fill="#FFD700"/>
    <circle cx="12" cy="32" r="1" fill="#FFD700"/>
    <circle cx="-2" cy="18" r="1" fill="#FFD700"/>
    <circle cx="22" cy="8" r="1" fill="#FFD700"/>
    <circle cx="22" cy="28" r="1" fill="#FFD700"/>
    <circle cx="2" cy="8" r="1" fill="#FFD700"/>
    <circle cx="2" cy="28" r="1" fill="#FFD700"/>
  </g>
</svg>
