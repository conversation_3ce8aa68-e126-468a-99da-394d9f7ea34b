update_state:
  name: Update YouTube State
  description: Manually update the YouTube viewing state
  fields:
    is_fullscreen:
      name: Is Fullscreen
      description: Whether YouTube is in fullscreen mode
      required: false
      selector:
        boolean:
    is_cinema_mode:
      name: Is Cinema Mode
      description: Whether YouTube is in cinema mode
      required: false
      selector:
        boolean:
    is_playing:
      name: Is Playing
      description: Whether a video is currently playing
      required: false
      selector:
        boolean:
    url:
      name: URL
      description: Current YouTube video URL
      required: false
      selector:
        text:

control_lights:
  name: Control Lights
  description: Manually control lights based on YouTube state
  fields:
    state:
      name: YouTube State
      description: The YouTube viewing state to simulate
      required: true
      selector:
        select:
          options:
            - "idle"
            - "playing"
            - "cinema_playing"
            - "cinema_paused"
            - "fullscreen_playing"
            - "fullscreen_paused"
    light_entities:
      name: Light Entities
      description: Specific light entities to control (optional, uses config if not specified)
      required: false
      selector:
        entity:
          domain: light
          multiple: true
