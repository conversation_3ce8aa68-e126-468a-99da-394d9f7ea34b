// YouTube Lights Controller - Popup Script
class PopupController {
  constructor() {
    this.elements = {};
    this.currentState = null;
    this.init();
  }

  init() {
    this.bindElements();
    this.bindEvents();
    this.loadSettings();
    this.updateCurrentState();
    
    // Update state every 2 seconds
    setInterval(() => this.updateCurrentState(), 2000);
  }

  bindElements() {
    this.elements = {
      loading: document.getElementById('loading'),
      mainContent: document.getElementById('main-content'),
      status: document.getElementById('status'),
      haUrl: document.getElementById('ha-url'),
      accessToken: document.getElementById('access-token'),
      webhookId: document.getElementById('webhook-id'),
      enabled: document.getElementById('enabled'),
      saveBtn: document.getElementById('save-btn'),
      testBtn: document.getElementById('test-btn'),
      cinemaState: document.getElementById('cinema-state'),
      fullscreenState: document.getElementById('fullscreen-state'),
      playingState: document.getElementById('playing-state')
    };
  }

  bindEvents() {
    this.elements.saveBtn.addEventListener('click', () => this.saveSettings());
    this.elements.testBtn.addEventListener('click', () => this.testConnection());
    
    // Auto-save on checkbox change
    this.elements.enabled.addEventListener('change', () => this.saveSettings());
  }

  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      
      this.elements.haUrl.value = response.homeAssistantUrl || '';
      this.elements.webhookId.value = response.webhookId || '';
      this.elements.enabled.checked = response.isEnabled !== false;
      
      // Don't populate access token for security
      if (response.hasToken) {
        this.elements.accessToken.placeholder = 'Token configured (hidden)';
      }
      
      this.updateConnectionStatus(!!response.homeAssistantUrl);
      
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showError('Failed to load settings');
    } finally {
      this.elements.loading.style.display = 'none';
      this.elements.mainContent.style.display = 'block';
    }
  }

  async saveSettings() {
    try {
      this.elements.saveBtn.textContent = 'Saving...';
      this.elements.saveBtn.disabled = true;
      
      const settings = {
        homeAssistantUrl: this.elements.haUrl.value.trim(),
        webhookId: this.elements.webhookId.value.trim(),
        isEnabled: this.elements.enabled.checked
      };
      
      // Only include access token if it's been changed
      if (this.elements.accessToken.value.trim()) {
        settings.accessToken = this.elements.accessToken.value.trim();
      }
      
      await chrome.runtime.sendMessage({
        type: 'SAVE_SETTINGS',
        settings: settings
      });
      
      this.updateConnectionStatus(!!settings.homeAssistantUrl);
      this.showSuccess('Settings saved successfully!');
      
      // Clear the access token field for security
      if (this.elements.accessToken.value.trim()) {
        this.elements.accessToken.value = '';
        this.elements.accessToken.placeholder = 'Token configured (hidden)';
      }
      
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showError('Failed to save settings');
    } finally {
      this.elements.saveBtn.textContent = 'Save Settings';
      this.elements.saveBtn.disabled = false;
    }
  }

  async testConnection() {
    try {
      this.elements.testBtn.textContent = 'Testing...';
      this.elements.testBtn.disabled = true;
      
      const result = await chrome.runtime.sendMessage({ type: 'TEST_CONNECTION' });
      
      if (result.success) {
        this.showSuccess(result.message || 'Connection successful!');
        this.updateConnectionStatus(true);
      } else {
        this.showError(result.error || 'Connection failed');
        this.updateConnectionStatus(false);
      }
      
    } catch (error) {
      console.error('Error testing connection:', error);
      this.showError('Failed to test connection');
      this.updateConnectionStatus(false);
    } finally {
      this.elements.testBtn.textContent = 'Test Connection';
      this.elements.testBtn.disabled = false;
    }
  }

  async updateCurrentState() {
    try {
      // Get current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (tab && tab.url && tab.url.includes('youtube.com/watch')) {
        console.log('On YouTube watch page, requesting state from content script');

        // Try to get state from content script
        try {
          const response = await chrome.tabs.sendMessage(tab.id, { type: 'GET_STATE' });
          console.log('Content script response:', response);

          if (response && response.state) {
            this.displayCurrentState(response.state);
            return;
          }
        } catch (error) {
          console.log('Content script not responding:', error);
          // Show that we're on YouTube but content script isn't responding
          this.displayCurrentState({
            isCinemaMode: false,
            isFullscreen: false,
            isPlaying: false,
            error: 'Content script not responding'
          });
          return;
        }
      }

      // Default state when not on YouTube
      this.displayCurrentState({
        isCinemaMode: false,
        isFullscreen: false,
        isPlaying: false,
        notOnYoutube: true
      });

    } catch (error) {
      console.error('Error updating current state:', error);
      this.displayCurrentState({
        isCinemaMode: false,
        isFullscreen: false,
        isPlaying: false,
        error: error.message
      });
    }
  }

  displayCurrentState(state) {
    // Handle error states
    if (state.error) {
      this.elements.cinemaState.textContent = 'Error';
      this.elements.fullscreenState.textContent = 'Error';
      this.elements.playingState.textContent = 'Error';

      this.elements.cinemaState.style.color = '#dc3545';
      this.elements.fullscreenState.style.color = '#dc3545';
      this.elements.playingState.style.color = '#dc3545';

      this.showError(`Content script error: ${state.error}`);
      return;
    }

    if (state.notOnYoutube) {
      this.elements.cinemaState.textContent = 'N/A';
      this.elements.fullscreenState.textContent = 'N/A';
      this.elements.playingState.textContent = 'N/A';

      this.elements.cinemaState.style.color = '#6c757d';
      this.elements.fullscreenState.style.color = '#6c757d';
      this.elements.playingState.style.color = '#6c757d';
      return;
    }

    // Normal state display
    this.elements.cinemaState.textContent = state.isCinemaMode ? 'Yes' : 'No';
    this.elements.fullscreenState.textContent = state.isFullscreen ? 'Yes' : 'No';
    this.elements.playingState.textContent = state.isPlaying ? 'Yes' : 'No';

    // Update colors
    this.elements.cinemaState.style.color = state.isCinemaMode ? '#28a745' : '#6c757d';
    this.elements.fullscreenState.style.color = state.isFullscreen ? '#28a745' : '#6c757d';
    this.elements.playingState.style.color = state.isPlaying ? '#28a745' : '#6c757d';
  }

  updateConnectionStatus(connected) {
    if (connected) {
      this.elements.status.textContent = 'Connected to Home Assistant';
      this.elements.status.className = 'status connected';
    } else {
      this.elements.status.textContent = 'Not Connected';
      this.elements.status.className = 'status disconnected';
    }
  }

  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  showError(message) {
    this.showMessage(message, 'error');
  }

  showMessage(message, type) {
    // Create temporary message element
    const messageEl = document.createElement('div');
    messageEl.textContent = message;
    messageEl.style.cssText = `
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      padding: 10px 15px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 1000;
      ${type === 'success' 
        ? 'background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;'
        : 'background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'
      }
    `;
    
    document.body.appendChild(messageEl);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (messageEl.parentNode) {
        messageEl.parentNode.removeChild(messageEl);
      }
    }, 3000);
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
