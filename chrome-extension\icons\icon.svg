<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="#FF0000" stroke="#CC0000" stroke-width="2"/>
  
  <!-- YouTube play button (triangle) -->
  <polygon points="50,45 50,83 85,64" fill="white"/>
  
  <!-- Light bulb icon -->
  <g transform="translate(85, 25)">
    <!-- Bulb body -->
    <ellipse cx="15" cy="20" rx="12" ry="16" fill="#FFD700" stroke="#FFA500" stroke-width="1.5"/>
    <!-- Bulb base -->
    <rect x="9" y="32" width="12" height="8" rx="2" fill="#E6E6E6" stroke="#CCCCCC" stroke-width="1"/>
    <!-- Light rays -->
    <line x1="15" y1="5" x2="15" y2="1" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="27" y1="12" x2="30" y2="9" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="30" y1="20" x2="34" y2="20" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="27" y1="28" x2="30" y2="31" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="3" y1="12" x2="0" y2="9" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="0" y1="20" x2="-4" y2="20" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
    <line x1="3" y1="28" x2="0" y2="31" stroke="#FFD700" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- Connection line between YouTube and light -->
  <path d="M 75 64 Q 85 50 95 40" stroke="#FFD700" stroke-width="2" fill="none" stroke-dasharray="3,2"/>
</svg>
