#!/usr/bin/env python3
"""
Create detailed PNG icons programmatically using PIL.
This creates more sophisticated icons that closely match the SVG designs.
"""

from PIL import Image, ImageDraw, ImageFilter
import math
from pathlib import Path

def create_gradient_background(size, color1, color2):
    """Create a radial gradient background."""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    max_radius = int(size * 0.9 // 2)
    
    for r in range(max_radius, 0, -1):
        # Interpolate between colors
        ratio = r / max_radius
        r_val = int(color1[0] * ratio + color2[0] * (1 - ratio))
        g_val = int(color1[1] * ratio + color2[1] * (1 - ratio))
        b_val = int(color1[2] * ratio + color2[2] * (1 - ratio))
        
        color = (r_val, g_val, b_val, 255)
        draw.ellipse([center - r, center - r, center + r, center + r], fill=color)
    
    return img

def draw_play_button(draw, size, center_x, center_y, color=(255, 255, 255, 255)):
    """Draw a YouTube-style play button triangle."""
    triangle_size = size // 6
    points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    draw.polygon(points, fill=color)

def draw_light_bulb(draw, x, y, size, with_rays=True):
    """Draw a light bulb with optional rays."""
    bulb_width = size // 3
    bulb_height = int(bulb_width * 1.3)
    
    # Bulb body (ellipse)
    bulb_color = (255, 215, 0, 255)  # Gold
    draw.ellipse([x - bulb_width//2, y - bulb_height//2, 
                  x + bulb_width//2, y + bulb_height//2], fill=bulb_color)
    
    # Bulb base (rectangle)
    base_height = size // 8
    base_color = (230, 230, 230, 255)  # Light gray
    draw.rectangle([x - bulb_width//3, y + bulb_height//2 - 2,
                    x + bulb_width//3, y + bulb_height//2 + base_height], fill=base_color)
    
    # Light rays
    if with_rays and size > 32:
        ray_color = (255, 215, 0, 200)  # Semi-transparent gold
        ray_length = size // 6
        ray_width = max(1, size // 32)
        
        # 8 rays around the bulb
        for angle in range(0, 360, 45):
            rad = math.radians(angle)
            start_x = x + int((bulb_width//2 + 2) * math.cos(rad))
            start_y = y + int((bulb_width//2 + 2) * math.sin(rad))
            end_x = x + int((bulb_width//2 + ray_length) * math.cos(rad))
            end_y = y + int((bulb_width//2 + ray_length) * math.sin(rad))
            
            draw.line([start_x, start_y, end_x, end_y], fill=ray_color, width=ray_width)

def create_icon_16():
    """Create 16x16 icon - simplified design."""
    size = 16
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Red circle background
    draw.ellipse([1, 1, size-1, size-1], fill=(255, 0, 0, 255))
    
    # White play button
    center = size // 2
    triangle_size = size // 4
    points = [
        (center - triangle_size//2, center - triangle_size//2),
        (center - triangle_size//2, center + triangle_size//2),
        (center + triangle_size//2, center)
    ]
    draw.polygon(points, fill=(255, 255, 255, 255))
    
    # Small light indicator
    draw.ellipse([size-6, 2, size-2, 6], fill=(255, 215, 0, 255))
    
    return img

def create_icon_48():
    """Create 48x48 icon - medium detail."""
    size = 48
    img = create_gradient_background(size, (255, 68, 68), (204, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Play button
    center = size // 2
    draw_play_button(draw, size * 2, center, center)
    
    # Light bulb in corner
    bulb_x = int(size * 0.8)
    bulb_y = int(size * 0.25)
    draw_light_bulb(draw, bulb_x, bulb_y, size // 3, with_rays=True)
    
    # Connection line
    line_color = (255, 215, 0, 180)
    draw.line([center + size//6, center, bulb_x - size//8, bulb_y], fill=line_color, width=2)
    
    return img

def create_icon_128():
    """Create 128x128 icon - full detail."""
    size = 128
    img = create_gradient_background(size, (255, 68, 68), (204, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Play button with shadow effect
    center = size // 2
    # Shadow
    draw_play_button(draw, size * 2, center + 2, center + 2, (0, 0, 0, 100))
    # Main button
    draw_play_button(draw, size * 2, center, center)
    
    # Light bulb
    bulb_x = int(size * 0.75)
    bulb_y = int(size * 0.3)
    draw_light_bulb(draw, bulb_x, bulb_y, size // 2, with_rays=True)
    
    # Connection line with dashes
    line_color = (255, 215, 0, 200)
    start_x, start_y = center + size//4, center
    end_x, end_y = bulb_x - size//8, bulb_y + size//8
    
    # Draw dashed line
    dash_length = 8
    line_length = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
    num_dashes = int(line_length // (dash_length * 2))
    
    for i in range(num_dashes):
        t1 = (i * 2 * dash_length) / line_length
        t2 = ((i * 2 + 1) * dash_length) / line_length
        if t2 > 1:
            t2 = 1
        
        x1 = int(start_x + t1 * (end_x - start_x))
        y1 = int(start_y + t1 * (end_y - start_y))
        x2 = int(start_x + t2 * (end_x - start_x))
        y2 = int(start_y + t2 * (end_y - start_y))
        
        draw.line([x1, y1, x2, y2], fill=line_color, width=3)
    
    # Add some sparkle effects
    sparkle_color = (255, 255, 153, 180)
    sparkle_positions = [(30, 40), (90, 90), (20, 100)]
    for sx, sy in sparkle_positions:
        draw.ellipse([sx-2, sy-2, sx+2, sy+2], fill=sparkle_color)
    
    return img

def main():
    """Create all PNG icons with detailed designs."""
    icons_dir = Path(__file__).parent
    
    print("Creating detailed PNG icons...")
    
    # Create icons
    icons = [
        (create_icon_16(), 'icon16.png'),
        (create_icon_48(), 'icon48.png'),
        (create_icon_128(), 'icon128.png'),
        (create_icon_128(), 'icon.png'),  # Use 128px version for main icon
    ]
    
    for img, filename in icons:
        filepath = icons_dir / filename
        img.save(filepath, 'PNG')
        print(f"✓ Created detailed {filename}")
    
    print(f"\nSuccessfully created {len(icons)} detailed PNG icons!")
    print("\nThese icons feature:")
    print("  - Gradient backgrounds")
    print("  - Detailed light bulb with rays")
    print("  - YouTube play button")
    print("  - Connection indicators")
    print("  - Size-appropriate detail levels")

if __name__ == "__main__":
    main()
