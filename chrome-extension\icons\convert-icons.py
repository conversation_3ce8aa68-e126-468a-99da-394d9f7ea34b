#!/usr/bin/env python3
"""
Convert SVG icons to PNG format for Chrome extension.
Requires: pip install cairosvg pillow
"""

import os
from pathlib import Path

try:
    import cairosvg
    from PIL import Image
    import io
except ImportError:
    print("Required packages not installed. Please run:")
    print("pip install cairosvg pillow")
    exit(1)

def svg_to_png(svg_path, png_path, size=None):
    """Convert SVG to PNG with optional resizing."""
    try:
        # Read SVG file
        with open(svg_path, 'r', encoding='utf-8') as f:
            svg_content = f.read()
        
        # Convert SVG to PNG bytes
        png_bytes = cairosvg.svg2png(bytestring=svg_content.encode('utf-8'))
        
        # If size is specified, resize the image
        if size:
            image = Image.open(io.BytesIO(png_bytes))
            image = image.resize((size, size), Image.Resampling.LANCZOS)
            image.save(png_path, 'PNG')
        else:
            with open(png_path, 'wb') as f:
                f.write(png_bytes)
        
        print(f"✓ Created {png_path}")
        
    except Exception as e:
        print(f"✗ Error converting {svg_path}: {e}")

def main():
    """Convert all SVG icons to PNG format."""
    icons_dir = Path(__file__).parent
    
    # Icon conversions with specific sizes
    conversions = [
        ('icon16.svg', 'icon16.png', 16),
        ('icon48.svg', 'icon48.png', 48),
        ('icon128.svg', 'icon128.png', 128),
        ('icon.svg', 'icon.png', 128),  # Main icon as 128px
    ]
    
    print("Converting SVG icons to PNG...")
    
    for svg_name, png_name, size in conversions:
        svg_path = icons_dir / svg_name
        png_path = icons_dir / png_name
        
        if svg_path.exists():
            svg_to_png(svg_path, png_path, size)
        else:
            print(f"✗ SVG file not found: {svg_path}")
    
    print("\nConversion complete!")
    print("\nGenerated files:")
    for _, png_name, _ in conversions:
        png_path = icons_dir / png_name
        if png_path.exists():
            print(f"  - {png_name}")

if __name__ == "__main__":
    main()
