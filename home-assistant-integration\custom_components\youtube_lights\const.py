"""Constants for the YouTube Lights Controller integration."""

DOMAIN = "youtube_lights"

# Configuration keys
CONF_WEBHOOK_ID = "webhook_id"
CONF_LIGHT_ENTITIES = "light_entities"
CONF_SENSOR_ENTITIES = "sensor_entities"
CONF_BRIGHTNESS_CINEMA = "brightness_cinema"
CONF_BRIGHTNESS_FULLSCREEN = "brightness_fullscreen"
CONF_BRIGHTNESS_NORMAL = "brightness_normal"
CONF_COLOR_CINEMA = "color_cinema"
CONF_COLOR_FULLSCREEN = "color_fullscreen"
CONF_TRANSITION_TIME = "transition_time"

# Default values
DEFAULT_BRIGHTNESS_CINEMA = 30
DEFAULT_BRIGHTNESS_FULLSCREEN = 10
DEFAULT_BRIGHTNESS_NORMAL = 100
DEFAULT_TRANSITION_TIME = 2
DEFAULT_COLOR_CINEMA = [255, 100, 50]  # Warm orange
DEFAULT_COLOR_FULLSCREEN = [255, 0, 0]  # Red

# YouTube states
YOUTUBE_STATE_IDLE = "idle"
YOUTUBE_STATE_PLAYING = "playing"
YOUTUBE_STATE_CINEMA_PLAYING = "cinema_playing"
YOUTUBE_STATE_CINEMA_PAUSED = "cinema_paused"
YOUTUBE_STATE_FULLSCREEN_PLAYING = "fullscreen_playing"
YOUTUBE_STATE_FULLSCREEN_PAUSED = "fullscreen_paused"

# Sensor attributes
ATTR_IS_FULLSCREEN = "is_fullscreen"
ATTR_IS_CINEMA_MODE = "is_cinema_mode"
ATTR_IS_PLAYING = "is_playing"
ATTR_LAST_UPDATE = "last_update"
ATTR_URL = "url"
ATTR_FRIENDLY_NAME = "friendly_name"

# Service names
SERVICE_UPDATE_STATE = "update_state"
SERVICE_CONTROL_LIGHTS = "control_lights"

# Event names
EVENT_YOUTUBE_STATE_CHANGED = "youtube_lights_state_changed"
