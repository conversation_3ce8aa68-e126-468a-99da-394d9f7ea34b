# Example Home Assistant Automations for YouTube Lights Controller
# Copy these to your automations.yaml or create them via the UI

# Basic automation to turn off kitchen lights during fullscreen
- alias: "YouTube Fullscreen - Turn off Kitchen Lights"
  description: "Turn off kitchen lights when YouTube enters fullscreen mode"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "fullscreen_playing"
  action:
    - service: light.turn_off
      target:
        entity_id: light.kitchen_lights
      data:
        transition: 2

# Restore kitchen lights when exiting fullscreen
- alias: "YouTube Exit Fullscreen - Restore Kitchen Lights"
  description: "Restore kitchen lights when exiting YouTube fullscreen"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      from: "fullscreen_playing"
  action:
    - service: light.turn_on
      target:
        entity_id: light.kitchen_lights
      data:
        brightness_pct: 80
        transition: 3

# Time-based cinema mode lighting
- alias: "YouTube Cinema Mode - Evening Lighting"
  description: "Special lighting for cinema mode in the evening"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "cinema_playing"
  condition:
    - condition: time
      after: "18:00:00"
      before: "23:00:00"
  action:
    - service: light.turn_on
      target:
        entity_id: light.tv_backlight
      data:
        brightness: 80
        color_name: orange
        transition: 2
    - service: light.turn_off
      target:
        entity_id: 
          - light.ceiling_lights
          - light.floor_lamp
      data:
        transition: 2

# Notification when entering fullscreen (useful for debugging)
- alias: "YouTube Fullscreen Notification"
  description: "Send notification when YouTube enters fullscreen"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "fullscreen_playing"
  action:
    - service: notify.mobile_app_your_phone
      data:
        title: "YouTube Lights"
        message: "Entered fullscreen mode - lights dimmed"
        data:
          icon: "mdi:youtube"

# Gradual light restoration when pausing
- alias: "YouTube Paused - Gradual Light Restore"
  description: "Gradually restore lights when video is paused for more than 30 seconds"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "cinema_paused"
      for: "00:00:30"
  action:
    - service: light.turn_on
      target:
        entity_id: light.living_room_lights
      data:
        brightness_pct: 50
        transition: 5

# Turn on bias lighting for fullscreen videos
- alias: "YouTube Fullscreen - Bias Lighting"
  description: "Turn on TV bias lighting during fullscreen videos"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "fullscreen_playing"
  action:
    - service: light.turn_on
      target:
        entity_id: light.tv_bias_lighting
      data:
        brightness: 30
        color_temp: 400
        transition: 1

# Turn off bias lighting when exiting fullscreen
- alias: "YouTube Exit Fullscreen - Turn off Bias Lighting"
  description: "Turn off TV bias lighting when exiting fullscreen"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      from: "fullscreen_playing"
  action:
    - service: light.turn_off
      target:
        entity_id: light.tv_bias_lighting
      data:
        transition: 2

# Scene-based automation
- alias: "YouTube States - Scene Control"
  description: "Activate different scenes based on YouTube state"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
  action:
    - choose:
        - conditions:
            - condition: state
              entity_id: sensor.youtube_lights_state
              state: "fullscreen_playing"
          sequence:
            - service: scene.turn_on
              target:
                entity_id: scene.movie_time
        - conditions:
            - condition: state
              entity_id: sensor.youtube_lights_state
              state: "cinema_playing"
          sequence:
            - service: scene.turn_on
              target:
                entity_id: scene.tv_watching
        - conditions:
            - condition: state
              entity_id: sensor.youtube_lights_state
              state: "idle"
          sequence:
            - service: scene.turn_on
              target:
                entity_id: scene.normal_evening

# Advanced: Motion sensor integration
- alias: "YouTube + Motion - Smart Lighting"
  description: "Only dim lights if someone is present (motion sensor)"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "cinema_playing"
  condition:
    - condition: state
      entity_id: binary_sensor.living_room_motion
      state: "on"
  action:
    - service: light.turn_on
      target:
        entity_id: light.living_room_lights
      data:
        brightness_pct: 25
        transition: 3

# Restore lights only if motion detected recently
- alias: "YouTube Idle - Motion Based Restore"
  description: "Restore lights when YouTube goes idle, but only if motion detected recently"
  trigger:
    - platform: state
      entity_id: sensor.youtube_lights_state
      to: "idle"
      for: "00:02:00"
  condition:
    - condition: state
      entity_id: binary_sensor.living_room_motion
      state: "on"
      for:
        hours: 0
        minutes: 5
        seconds: 0
  action:
    - service: light.turn_on
      target:
        entity_id: light.living_room_lights
      data:
        brightness_pct: 80
        transition: 4
