<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <radialGradient id="redGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#FF4444"/>
      <stop offset="100%" stop-color="#CC0000"/>
    </radialGradient>
    <radialGradient id="lightGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#FFFF99"/>
      <stop offset="100%" stop-color="#FFD700"/>
    </radialGradient>
    <linearGradient id="baseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F0F0F0"/>
      <stop offset="100%" stop-color="#CCCCCC"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle with gradient -->
  <circle cx="64" cy="64" r="60" fill="url(#redGradient)" stroke="#990000" stroke-width="2"/>
  
  <!-- YouTube play button with shadow -->
  <polygon points="50,45 50,83 85,64" fill="white" filter="drop-shadow(2px 2px 4px rgba(0,0,0,0.3))"/>
  
  <!-- Light bulb assembly -->
  <g transform="translate(85, 25)">
    <!-- Bulb body with gradient -->
    <ellipse cx="15" cy="20" rx="12" ry="16" fill="url(#lightGradient)" stroke="#FFA500" stroke-width="1.5"/>
    
    <!-- Filament inside bulb -->
    <path d="M 10 15 Q 15 12 20 15 Q 15 18 10 15" stroke="#FF8800" stroke-width="1" fill="none"/>
    <path d="M 10 20 Q 15 17 20 20 Q 15 23 10 20" stroke="#FF8800" stroke-width="1" fill="none"/>
    <path d="M 10 25 Q 15 22 20 25 Q 15 28 10 25" stroke="#FF8800" stroke-width="1" fill="none"/>
    
    <!-- Bulb base with gradient -->
    <rect x="9" y="32" width="12" height="8" rx="2" fill="url(#baseGradient)" stroke="#999999" stroke-width="1"/>
    <rect x="10" y="33" width="10" height="1" fill="#DDDDDD"/>
    <rect x="10" y="35" width="10" height="1" fill="#DDDDDD"/>
    <rect x="10" y="37" width="10" height="1" fill="#DDDDDD"/>
    
    <!-- Light rays with glow effect -->
    <g stroke="#FFD700" stroke-width="3" stroke-linecap="round" opacity="0.8">
      <line x1="15" y1="5" x2="15" y2="1"/>
      <line x1="27" y1="12" x2="31" y2="8"/>
      <line x1="31" y1="20" x2="35" y2="20"/>
      <line x1="27" y1="28" x2="31" y2="32"/>
      <line x1="3" y1="12" x2="-1" y2="8"/>
      <line x1="-1" y1="20" x2="-5" y2="20"/>
      <line x1="3" y1="28" x2="-1" y2="32"/>
    </g>
    
    <!-- Brighter inner rays -->
    <g stroke="#FFFF99" stroke-width="1.5" stroke-linecap="round">
      <line x1="15" y1="5" x2="15" y2="1"/>
      <line x1="27" y1="12" x2="31" y2="8"/>
      <line x1="31" y1="20" x2="35" y2="20"/>
      <line x1="27" y1="28" x2="31" y2="32"/>
      <line x1="3" y1="12" x2="-1" y2="8"/>
      <line x1="-1" y1="20" x2="-5" y2="20"/>
      <line x1="3" y1="28" x2="-1" y2="32"/>
    </g>
  </g>
  
  <!-- Connection line with animation effect -->
  <path d="M 75 64 Q 85 50 95 40" stroke="#FFD700" stroke-width="3" fill="none" stroke-dasharray="5,3" opacity="0.8"/>
  <path d="M 75 64 Q 85 50 95 40" stroke="#FFFF99" stroke-width="1.5" fill="none" stroke-dasharray="5,3"/>
  
  <!-- Small sparkles for magic effect -->
  <g fill="#FFFF99" opacity="0.7">
    <polygon points="45,35 47,39 51,37 47,41 45,45 43,41 39,37 43,39" transform="scale(0.3)"/>
    <polygon points="95,75 97,79 101,77 97,81 95,85 93,81 89,77 93,79" transform="scale(0.25)"/>
    <polygon points="25,85 27,89 31,87 27,91 25,95 23,91 19,87 23,89" transform="scale(0.2)"/>
  </g>
</svg>
