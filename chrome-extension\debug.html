<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>YouTube Lights Controller - Debug</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .log-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 YouTube Lights Controller - Debug Page</h1>
        
        <div class="instructions">
            <h3>🔧 Debugging Steps</h3>
            <div class="step">
                <strong>Step 1:</strong> Make sure you're on a YouTube video page (youtube.com/watch?v=...)
            </div>
            <div class="step">
                <strong>Step 2:</strong> Open Chrome DevTools (F12) and go to the Console tab
            </div>
            <div class="step">
                <strong>Step 3:</strong> Look for messages starting with "🎬" from the content script
            </div>
            <div class="step">
                <strong>Step 4:</strong> Click the buttons below to test extension functionality
            </div>
        </div>
        
        <div class="debug-section">
            <h3>Extension Status</h3>
            <div id="extension-status" class="status warning">Checking extension...</div>
            <button onclick="checkExtensionStatus()">Check Extension Status</button>
        </div>
        
        <div class="debug-section">
            <h3>YouTube Detection</h3>
            <div id="youtube-status" class="status warning">Checking YouTube page...</div>
            <button onclick="checkYouTubeStatus()">Check YouTube Status</button>
        </div>
        
        <div class="debug-section">
            <h3>Content Script Communication</h3>
            <div id="content-script-status" class="status warning">Not tested</div>
            <button onclick="testContentScript()">Test Content Script</button>
        </div>
        
        <div class="debug-section">
            <h3>Manual State Check</h3>
            <button onclick="checkVideoState()">Check Video State</button>
            <button onclick="checkCinemaMode()">Check Cinema Mode</button>
            <button onclick="checkFullscreen()">Check Fullscreen</button>
            <div id="manual-results" class="log-output" style="margin-top: 10px; min-height: 100px;">
Click buttons above to run manual checks...
            </div>
        </div>
        
        <div class="debug-section">
            <h3>Console Logs</h3>
            <p>Check the browser console (F12 → Console) for detailed logs from the content script.</p>
            <p>Look for messages starting with "🎬" to see what the extension is detecting.</p>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('manual-results');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function setStatus(elementId, message, type = 'warning') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function checkExtensionStatus() {
            try {
                // Check if chrome.runtime is available
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    setStatus('extension-status', '✅ Extension API available', 'success');
                    log('Extension API is available');
                } else {
                    setStatus('extension-status', '❌ Extension API not available', 'error');
                    log('Extension API not available - extension may not be loaded');
                }
            } catch (error) {
                setStatus('extension-status', `❌ Error: ${error.message}`, 'error');
                log(`Extension check error: ${error.message}`);
            }
        }

        function checkYouTubeStatus() {
            const isYouTube = window.location.hostname === 'www.youtube.com';
            const isWatchPage = window.location.pathname === '/watch';
            
            if (isYouTube && isWatchPage) {
                setStatus('youtube-status', '✅ On YouTube watch page', 'success');
                log('Currently on YouTube watch page');
            } else if (isYouTube) {
                setStatus('youtube-status', '⚠️ On YouTube but not watch page', 'warning');
                log('On YouTube but not on a video watch page');
            } else {
                setStatus('youtube-status', '❌ Not on YouTube', 'error');
                log('Not on YouTube - extension only works on youtube.com/watch pages');
            }
        }

        async function testContentScript() {
            try {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                const response = await chrome.tabs.sendMessage(tab.id, { type: 'GET_STATE' });
                
                if (response && response.state) {
                    setStatus('content-script-status', '✅ Content script responding', 'success');
                    log(`Content script response: ${JSON.stringify(response.state, null, 2)}`);
                } else {
                    setStatus('content-script-status', '⚠️ Content script not responding properly', 'warning');
                    log('Content script responded but with no state data');
                }
            } catch (error) {
                setStatus('content-script-status', '❌ Content script not responding', 'error');
                log(`Content script error: ${error.message}`);
                log('Make sure you are on a YouTube video page and the extension is loaded');
            }
        }

        function checkVideoState() {
            const video = document.querySelector('video');
            if (video) {
                const state = {
                    paused: video.paused,
                    ended: video.ended,
                    readyState: video.readyState,
                    currentTime: video.currentTime,
                    duration: video.duration
                };
                log(`Video element found: ${JSON.stringify(state, null, 2)}`);
            } else {
                log('No video element found on this page');
            }
        }

        function checkCinemaMode() {
            const ytdApp = document.querySelector('ytd-app');
            if (ytdApp) {
                const hasTheater = ytdApp.hasAttribute('theater');
                const hasTheaterClass = ytdApp.classList.contains('theater');
                const hasMastheadHidden = ytdApp.hasAttribute('masthead-hidden');
                
                log(`Cinema mode detection:
- theater attribute: ${hasTheater}
- theater class: ${hasTheaterClass}  
- masthead-hidden: ${hasMastheadHidden}`);
            } else {
                log('ytd-app element not found');
            }
        }

        function checkFullscreen() {
            const fullscreenElement = document.fullscreenElement || 
                                    document.webkitFullscreenElement ||
                                    document.mozFullScreenElement ||
                                    document.msFullscreenElement;
            
            log(`Fullscreen state: ${!!fullscreenElement}`);
            if (fullscreenElement) {
                log(`Fullscreen element: ${fullscreenElement.tagName}`);
            }
        }

        // Auto-run initial checks
        window.addEventListener('load', () => {
            checkExtensionStatus();
            checkYouTubeStatus();
        });
    </script>
</body>
</html>
