# YouTube Lights Controller

A Chrome extension and Home Assistant integration that automatically controls your smart lights based on YouTube viewing state (cinema mode, fullscreen, playing/paused).

## Features

- **Chrome Extension**: Detects YouTube cinema mode, fullscreen, and play/pause states
- **Home Assistant Integration**: Receives YouTube state and controls lights automatically
- **Smart Light Control**: Different brightness levels for cinema mode, fullscreen, and normal viewing
- **Real-time Updates**: Instant light adjustments when YouTube state changes
- **Configurable**: Customizable brightness levels, transition times, and light entities

## Components

### 1. Chrome Extension (`chrome-extension/`)
- Monitors YouTube pages for state changes
- Detects cinema mode, fullscreen, and play/pause events
- Sends state updates to Home Assistant via webhook or API
- User-friendly popup for configuration

### 2. Home Assistant Integration (`home-assistant-integration/`)
- Custom component that receives YouTube state updates
- Automatically controls configured light entities
- Provides sensor entity with current YouTube state
- Configurable brightness levels and transition times

## Installation

### Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the `chrome-extension` folder
4. The extension icon should appear in your toolbar

### Home Assistant Integration

1. Copy the `home-assistant-integration/custom_components/youtube_lights` folder to your Home Assistant `custom_components` directory:
   ```
   /config/custom_components/youtube_lights/
   ```

2. Restart Home Assistant

3. Go to Settings → Devices & Services → Add Integration

4. Search for "YouTube Lights Controller" and follow the setup wizard

## Configuration

### Chrome Extension Setup

1. Click the extension icon in Chrome
2. Enter your Home Assistant URL (e.g., `http://homeassistant.local:8123`)
3. Create a Long-Lived Access Token in Home Assistant:
   - Go to Profile → Security → Long-Lived Access Tokens
   - Click "Create Token" and copy the token
4. Paste the token in the extension
5. (Optional) Set a webhook ID for webhook-based communication
6. Click "Save Settings" and "Test Connection"

### Home Assistant Integration Setup

1. During integration setup, configure:
   - **Webhook ID**: Must match the one in Chrome extension (if using webhooks)
   - **Light Entities**: Select which lights to control
   - **Brightness Levels**:
     - Cinema Mode: Default 30% (warm, dim lighting)
     - Fullscreen: Default 10% (minimal lighting)
     - Normal: Default 100% (full brightness)
   - **Transition Time**: How long brightness changes take (default 2 seconds)

## Usage

1. Install and configure both components
2. Open YouTube in Chrome
3. Play a video and switch to cinema mode or fullscreen
4. Watch your lights automatically adjust!

### YouTube States

The system recognizes these states:
- **idle**: No video playing, normal lighting
- **playing**: Video playing in normal mode
- **cinema_playing**: Video playing in cinema mode (dimmed lights)
- **cinema_paused**: Video paused in cinema mode
- **fullscreen_playing**: Video playing in fullscreen (minimal lighting)
- **fullscreen_paused**: Video paused in fullscreen

## Automation Examples

You can create additional automations using the YouTube state sensor:

```yaml
# Turn off other lights when entering fullscreen
automation:
  - alias: "YouTube Fullscreen - Turn off other lights"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        to: "fullscreen_playing"
    action:
      - service: light.turn_off
        target:
          entity_id: 
            - light.kitchen_lights
            - light.hallway_lights
        data:
          transition: 2

  # Restore lights when exiting fullscreen
  - alias: "YouTube Exit Fullscreen - Restore lights"
    trigger:
      - platform: state
        entity_id: sensor.youtube_lights_state
        from: "fullscreen_playing"
    action:
      - service: light.turn_on
        target:
          entity_id: 
            - light.kitchen_lights
            - light.hallway_lights
        data:
          brightness_pct: 80
          transition: 2
```

## Troubleshooting

### Chrome Extension Issues

1. **Extension not detecting YouTube state**:
   - Refresh the YouTube page
   - Check browser console for errors (F12 → Console)
   - Ensure you're on a YouTube video page (`youtube.com/watch`)

2. **Connection to Home Assistant failing**:
   - Verify Home Assistant URL is correct and accessible
   - Check that the access token is valid
   - Ensure Home Assistant is running and reachable

### Home Assistant Integration Issues

1. **Webhook not receiving data**:
   - Verify webhook ID matches between extension and HA
   - Check Home Assistant logs for webhook errors
   - Test with a manual webhook call

2. **Lights not responding**:
   - Verify light entities are configured correctly
   - Check that lights are available and responsive
   - Review Home Assistant logs for service call errors

## Development

### Chrome Extension Development

- Content script: `chrome-extension/content.js`
- Background script: `chrome-extension/background.js`
- Popup UI: `chrome-extension/popup.html` and `chrome-extension/popup.js`

### Home Assistant Integration Development

- Main integration: `home-assistant-integration/custom_components/youtube_lights/__init__.py`
- Sensor platform: `home-assistant-integration/custom_components/youtube_lights/sensor.py`
- Config flow: `home-assistant-integration/custom_components/youtube_lights/config_flow.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter issues or have questions:
1. Check the troubleshooting section above
2. Review Home Assistant and browser console logs
3. Create an issue on GitHub with detailed information

## Changelog

### v1.0.0
- Initial release
- Chrome extension with YouTube state detection
- Home Assistant integration with automatic light control
- Configurable brightness levels and transitions
- Webhook and API communication support
