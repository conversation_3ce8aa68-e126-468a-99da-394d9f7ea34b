<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
      font-weight: bold;
    }
    
    .status.connected {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.disconnected {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }
    
    .form-group input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: #4285f4;
      box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .checkbox-group input[type="checkbox"] {
      margin-right: 8px;
      width: auto;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background-color: #4285f4;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #3367d6;
    }
    
    .btn-secondary {
      background-color: #f8f9fa;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .btn-secondary:hover {
      background-color: #e2e6ea;
    }
    
    .current-state {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 12px;
    }
    
    .current-state h3 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #333;
    }
    
    .state-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
    }
    
    .state-value {
      font-weight: 500;
    }
    
    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    
    .loading {
      text-align: center;
      color: #666;
      padding: 20px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎬 YouTube Lights Controller</h1>
  </div>
  
  <div id="loading" class="loading">
    Loading...
  </div>
  
  <div id="main-content" style="display: none;">
    <div id="status" class="status disconnected">
      Not Connected
    </div>
    
    <div class="current-state">
      <h3>Current State</h3>
      <div class="state-item">
        <span>Cinema Mode:</span>
        <span id="cinema-state" class="state-value">-</span>
      </div>
      <div class="state-item">
        <span>Fullscreen:</span>
        <span id="fullscreen-state" class="state-value">-</span>
      </div>
      <div class="state-item">
        <span>Playing:</span>
        <span id="playing-state" class="state-value">-</span>
      </div>
    </div>
    
    <div class="form-group">
      <label for="ha-url">Home Assistant URL:</label>
      <input type="url" id="ha-url" placeholder="http://homeassistant.local:8123">
      <div class="help-text">Include http:// or https://</div>
    </div>
    
    <div class="form-group">
      <label for="access-token">Long-Lived Access Token:</label>
      <input type="password" id="access-token" placeholder="Your HA access token">
      <div class="help-text">Create in HA Profile → Security → Long-Lived Access Tokens</div>
    </div>
    
    <div class="form-group">
      <label for="webhook-id">Webhook ID (Optional):</label>
      <input type="text" id="webhook-id" placeholder="youtube_lights_webhook">
      <div class="help-text">For webhook automation triggers</div>
    </div>
    
    <div class="checkbox-group">
      <input type="checkbox" id="enabled" checked>
      <label for="enabled">Enable light control</label>
    </div>
    
    <div class="button-group">
      <button id="save-btn" class="btn-primary">Save Settings</button>
      <button id="test-btn" class="btn-secondary">Test Connection</button>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
