#!/usr/bin/env python3
"""
Simple SVG to PNG converter using wand (ImageMagick Python binding)
Alternative to cairosvg for Windows users.

Install with: pip install Wand
Requires ImageMagick to be installed: https://imagemagick.org/script/download.php#windows
"""

import os
from pathlib import Path

try:
    from wand.image import Image as WandImage
    from wand.color import Color
except ImportError:
    print("Wand library not installed. Please run:")
    print("pip install Wand")
    print("\nAlso install ImageMagick from: https://imagemagick.org/script/download.php#windows")
    exit(1)

def svg_to_png_wand(svg_path, png_path, size):
    """Convert SVG to PNG using Wand (ImageMagick)."""
    try:
        with WandImage() as img:
            with WandImage(filename=str(svg_path)) as svg:
                svg.format = 'png'
                svg.resize(size, size)
                svg.save(filename=str(png_path))
        
        print(f"✓ Created {png_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error converting {svg_path}: {e}")
        return False

def main():
    """Convert all SVG icons to PNG format using Wand."""
    icons_dir = Path(__file__).parent
    
    conversions = [
        ('icon16.svg', 'icon16.png', 16),
        ('icon48.svg', 'icon48.png', 48),
        ('icon128.svg', 'icon128.png', 128),
        ('icon.svg', 'icon.png', 128),
    ]
    
    print("Converting SVG icons to PNG using ImageMagick...")
    
    success_count = 0
    for svg_name, png_name, size in conversions:
        svg_path = icons_dir / svg_name
        png_path = icons_dir / png_name
        
        if svg_path.exists():
            if svg_to_png_wand(svg_path, png_path, size):
                success_count += 1
        else:
            print(f"✗ SVG file not found: {svg_path}")
    
    print(f"\nConversion complete! Successfully converted {success_count}/{len(conversions)} icons.")
    
    if success_count > 0:
        print("\nGenerated files:")
        for _, png_name, _ in conversions:
            png_path = icons_dir / png_name
            if png_path.exists():
                print(f"  - {png_name}")

if __name__ == "__main__":
    main()
