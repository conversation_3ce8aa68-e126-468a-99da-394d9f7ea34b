@echo off
REM Test script for YouTube Lights Controller webhook (Windows version)
REM Usage: test-webhook.bat [your-ha-url] [webhook-id]

REM Default values (change these to match your setup)
set "HA_URL=%~1"
set "WEBHOOK_ID=%~2"

if "%HA_URL%"=="" set "HA_URL=http://homeassistant.local:8123"
if "%WEBHOOK_ID%"=="" set "WEBHOOK_ID=youtube_lights_webhook"

echo Testing YouTube Lights Controller webhook...
echo Home Assistant URL: %HA_URL%
echo Webhook ID: %WEBHOOK_ID%
echo.

REM Test 1: Cinema mode playing
echo Test 1: Cinema mode playing
curl -X POST "%HA_URL%/api/webhook/%WEBHOOK_ID%" -H "Content-Type: application/json" -d "{\"youtube_state\":{\"isFullscreen\":false,\"isCinemaMode\":true,\"isPlaying\":true,\"timestamp\":%date:~-4%%date:~4,2%%date:~7,2%000,\"url\":\"https://youtube.com/watch?v=test1\"},\"source\":\"test_script\"}"
echo.

timeout /t 3 /nobreak >nul

REM Test 2: Fullscreen playing
echo Test 2: Fullscreen playing
curl -X POST "%HA_URL%/api/webhook/%WEBHOOK_ID%" -H "Content-Type: application/json" -d "{\"youtube_state\":{\"isFullscreen\":true,\"isCinemaMode\":false,\"isPlaying\":true,\"timestamp\":%date:~-4%%date:~4,2%%date:~7,2%000,\"url\":\"https://youtube.com/watch?v=test2\"},\"source\":\"test_script\"}"
echo.

timeout /t 3 /nobreak >nul

REM Test 3: Paused
echo Test 3: Video paused
curl -X POST "%HA_URL%/api/webhook/%WEBHOOK_ID%" -H "Content-Type: application/json" -d "{\"youtube_state\":{\"isFullscreen\":true,\"isCinemaMode\":false,\"isPlaying\":false,\"timestamp\":%date:~-4%%date:~4,2%%date:~7,2%000,\"url\":\"https://youtube.com/watch?v=test2\"},\"source\":\"test_script\"}"
echo.

timeout /t 3 /nobreak >nul

REM Test 4: Back to idle
echo Test 4: Back to idle
curl -X POST "%HA_URL%/api/webhook/%WEBHOOK_ID%" -H "Content-Type: application/json" -d "{\"youtube_state\":{\"isFullscreen\":false,\"isCinemaMode\":false,\"isPlaying\":false,\"timestamp\":%date:~-4%%date:~4,2%%date:~7,2%000,\"url\":null},\"source\":\"test_script\"}"
echo.

echo Test completed! Check your Home Assistant sensor.youtube_lights_state for updates.
pause
