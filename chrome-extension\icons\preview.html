<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Lights Controller - Icon Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .icon-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .icon-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .icon-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .icon-display {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 120px;
            border: 1px solid #dee2e6;
        }
        
        .icon-sizes {
            display: flex;
            justify-content: center;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .size-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .dark-bg {
            background: #2c3e50;
        }
        
        .light-bg {
            background: #ecf0f1;
        }
        
        .toolbar-demo {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 8px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 10px;
        }
        
        .usage-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .usage-section h2 {
            margin-top: 0;
            color: #333;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 YouTube Lights Controller</h1>
        <p class="subtitle">Chrome Extension Icon Preview</p>
        
        <div class="icon-grid">
            <!-- Main Icon Set -->
            <div class="icon-card">
                <h3>Main Icon Set</h3>
                <div class="icon-display">
                    <div class="icon-sizes">
                        <div>
                            <div class="size-label">16px</div>
                            <img src="icon16.svg" width="16" height="16" alt="16px icon">
                        </div>
                        <div>
                            <div class="size-label">48px</div>
                            <img src="icon48.svg" width="48" height="48" alt="48px icon">
                        </div>
                        <div>
                            <div class="size-label">128px</div>
                            <img src="icon128.svg" width="128" height="128" alt="128px icon">
                        </div>
                    </div>
                </div>
                <p>Standard icons with gradients and detailed light effects</p>
            </div>
            
            <!-- Dark Theme -->
            <div class="icon-card">
                <h3>Dark Theme</h3>
                <div class="icon-display dark-bg">
                    <img src="icon-dark.svg" width="128" height="128" alt="Dark theme icon">
                </div>
                <p>Optimized for dark backgrounds and themes</p>
            </div>
            
            <!-- Minimal Design -->
            <div class="icon-card">
                <h3>Minimal Design</h3>
                <div class="icon-display">
                    <img src="icon-minimal.svg" width="128" height="128" alt="Minimal icon">
                </div>
                <p>Clean, flat design with simple elements</p>
            </div>
            
            <!-- Cinema Theme -->
            <div class="icon-card">
                <h3>Cinema Theme</h3>
                <div class="icon-display">
                    <img src="icon-cinema.svg" width="128" height="128" alt="Cinema icon">
                </div>
                <p>Film strip design emphasizing cinema mode</p>
            </div>
        </div>
        
        <!-- Toolbar Demo -->
        <div style="text-align: center; margin: 30px 0;">
            <h3>Toolbar Preview</h3>
            <div class="toolbar-demo">
                <img src="icon16.svg" width="16" height="16" alt="Toolbar icon">
                <span style="font-size: 13px; color: #5f6368;">YouTube Lights Controller</span>
            </div>
        </div>
        
        <!-- Usage Information -->
        <div class="usage-section">
            <h2>Icon Features</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">▶</div>
                    <span>YouTube play button</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💡</div>
                    <span>Smart light bulb</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔗</div>
                    <span>Connection indicator</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✨</div>
                    <span>Light ray effects</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎨</div>
                    <span>Gradient backgrounds</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <span>Multiple sizes</span>
                </div>
            </div>
            
            <h3>Converting to PNG</h3>
            <p>Run the conversion script to generate PNG files for the Chrome extension:</p>
            <div class="code-block">
# Windows
convert-icons.bat

# Linux/Mac  
python3 convert-icons.py
            </div>
            
            <h3>Manifest Configuration</h3>
            <p>The extension manifest is configured to use these icons:</p>
            <div class="code-block">
"icons": {
  "16": "icons/icon16.png",
  "48": "icons/icon48.png", 
  "128": "icons/icon128.png"
}
            </div>
        </div>
    </div>
</body>
</html>
