// YouTube Lights Controller - Background Script
class BackgroundController {
  constructor() {
    this.homeAssistantUrl = '';
    this.accessToken = '';
    this.webhookId = '';
    this.isEnabled = true;
    this.init();
  }

  async init() {
    console.log('Background script initialized');
    
    // Load settings from storage
    await this.loadSettings();
    
    // Listen for messages from content script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Listen for tab updates to detect YouTube navigation
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && 
          tab.url && tab.url.includes('youtube.com/watch')) {
        console.log('YouTube video page loaded');
      }
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'homeAssistantUrl',
        'accessToken', 
        'webhookId',
        'isEnabled'
      ]);
      
      this.homeAssistantUrl = result.homeAssistantUrl || '';
      this.accessToken = result.accessToken || '';
      this.webhookId = result.webhookId || '';
      this.isEnabled = result.isEnabled !== false; // Default to true
      
      console.log('Settings loaded:', {
        hasUrl: !!this.homeAssistantUrl,
        hasToken: !!this.accessToken,
        hasWebhook: !!this.webhookId,
        isEnabled: this.isEnabled
      });
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'YOUTUBE_STATE_CHANGE':
        await this.handleStateChange(message.state);
        sendResponse({ success: true });
        break;
        
      case 'GET_SETTINGS':
        sendResponse({
          homeAssistantUrl: this.homeAssistantUrl,
          hasToken: !!this.accessToken,
          webhookId: this.webhookId,
          isEnabled: this.isEnabled
        });
        break;
        
      case 'SAVE_SETTINGS':
        await this.saveSettings(message.settings);
        sendResponse({ success: true });
        break;
        
      case 'TEST_CONNECTION':
        const testResult = await this.testConnection();
        sendResponse(testResult);
        break;
        
      default:
        sendResponse({ error: 'Unknown message type' });
    }
  }

  async handleStateChange(state) {
    if (!this.isEnabled) {
      console.log('Extension disabled, skipping state change');
      return;
    }

    console.log('Handling state change:', state);
    
    // Send to Home Assistant via webhook
    if (this.webhookId) {
      await this.sendWebhook(state);
    }
    
    // Send to Home Assistant via API
    if (this.homeAssistantUrl && this.accessToken) {
      await this.sendToHomeAssistant(state);
    }
  }

  async sendWebhook(state) {
    try {
      const webhookUrl = `${this.homeAssistantUrl}/api/webhook/${this.webhookId}`;
      
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          youtube_state: state,
          source: 'youtube_lights_extension'
        })
      });
      
      if (response.ok) {
        console.log('Webhook sent successfully');
      } else {
        console.error('Webhook failed:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error sending webhook:', error);
    }
  }

  async sendToHomeAssistant(state) {
    try {
      const apiUrl = `${this.homeAssistantUrl}/api/states/sensor.youtube_lights_state`;
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          state: this.getStateString(state),
          attributes: {
            ...state,
            friendly_name: 'YouTube Lights State',
            icon: 'mdi:youtube'
          }
        })
      });
      
      if (response.ok) {
        console.log('State sent to Home Assistant successfully');
      } else {
        console.error('Home Assistant API failed:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error sending to Home Assistant:', error);
    }
  }

  getStateString(state) {
    if (state.isFullscreen && state.isPlaying) return 'fullscreen_playing';
    if (state.isFullscreen) return 'fullscreen_paused';
    if (state.isCinemaMode && state.isPlaying) return 'cinema_playing';
    if (state.isCinemaMode) return 'cinema_paused';
    if (state.isPlaying) return 'playing';
    return 'idle';
  }

  async saveSettings(settings) {
    try {
      this.homeAssistantUrl = settings.homeAssistantUrl || '';
      this.accessToken = settings.accessToken || '';
      this.webhookId = settings.webhookId || '';
      this.isEnabled = settings.isEnabled !== false;
      
      await chrome.storage.sync.set({
        homeAssistantUrl: this.homeAssistantUrl,
        accessToken: this.accessToken,
        webhookId: this.webhookId,
        isEnabled: this.isEnabled
      });
      
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }

  async testConnection() {
    try {
      if (!this.homeAssistantUrl) {
        return { success: false, error: 'No Home Assistant URL configured' };
      }
      
      const response = await fetch(`${this.homeAssistantUrl}/api/`, {
        headers: this.accessToken ? {
          'Authorization': `Bearer ${this.accessToken}`
        } : {}
      });
      
      if (response.ok) {
        return { success: true, message: 'Connection successful' };
      } else {
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Initialize background controller
new BackgroundController();
