"""Config flow for YouTube Lights Controller integration."""
import logging
from typing import Any, Dict, Optional

import voluptuous as vol
from homeassistant import config_entries
from homeassistant.const import CONF_NAME
from homeassistant.core import HomeAssistant, callback
from homeassistant.data_entry_flow import FlowResult
from homeassistant.helpers import config_validation as cv

from .const import (
    DOM<PERSON><PERSON>,
    CONF_WEBHOOK_ID,
    CONF_LIGHT_ENTITIES,
    CONF_BRIGHTNESS_CINEMA,
    CONF_BRIGHTNESS_FULLSCREEN,
    CONF_BRIGHTNESS_NORMAL,
    CONF_TRANSITION_TIME,
    DEFAULT_BRIGHTNESS_CINEMA,
    DEFAULT_BRIGHTNESS_FULLSCREEN,
    DEFAULT_BRIGHTNESS_NORMAL,
    DEFAULT_TRANSITION_TIME,
)

_LOGGER = logging.getLogger(__name__)


class YouTubeLightsConfigFlow(config_entries.ConfigFlow, domain=DOMAIN):
    """Handle a config flow for YouTube Lights Controller."""

    VERSION = 1

    async def async_step_user(
        self, user_input: Optional[Dict[str, Any]] = None
    ) -> FlowResult:
        """Handle the initial step."""
        errors = {}

        if user_input is not None:
            # Validate webhook ID if provided
            webhook_id = user_input.get(CONF_WEBHOOK_ID, "").strip()
            if webhook_id and not webhook_id.replace("_", "").replace("-", "").isalnum():
                errors[CONF_WEBHOOK_ID] = "invalid_webhook_id"
            
            if not errors:
                return self.async_create_entry(
                    title="YouTube Lights Controller",
                    data=user_input,
                )

        # Get available light entities
        light_entities = []
        for entity_id in self.hass.states.async_entity_ids("light"):
            state = self.hass.states.get(entity_id)
            if state:
                light_entities.append(entity_id)

        data_schema = vol.Schema(
            {
                vol.Optional(CONF_WEBHOOK_ID, default=""): cv.string,
                vol.Optional(CONF_LIGHT_ENTITIES, default=[]): cv.multi_select(
                    {entity: entity for entity in light_entities}
                ),
                vol.Optional(
                    CONF_BRIGHTNESS_CINEMA, default=DEFAULT_BRIGHTNESS_CINEMA
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_BRIGHTNESS_FULLSCREEN, default=DEFAULT_BRIGHTNESS_FULLSCREEN
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_BRIGHTNESS_NORMAL, default=DEFAULT_BRIGHTNESS_NORMAL
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_TRANSITION_TIME, default=DEFAULT_TRANSITION_TIME
                ): vol.All(int, vol.Range(min=0, max=60)),
            }
        )

        return self.async_show_form(
            step_id="user",
            data_schema=data_schema,
            errors=errors,
            description_placeholders={
                "webhook_url": f"{self.hass.config.external_url or 'http://homeassistant.local:8123'}/api/webhook/{{webhook_id}}"
            },
        )

    @staticmethod
    @callback
    def async_get_options_flow(config_entry):
        """Get the options flow for this handler."""
        return YouTubeLightsOptionsFlow(config_entry)


class YouTubeLightsOptionsFlow(config_entries.OptionsFlow):
    """Handle options flow for YouTube Lights Controller."""

    def __init__(self, config_entry: config_entries.ConfigEntry) -> None:
        """Initialize options flow."""
        self.config_entry = config_entry

    async def async_step_init(
        self, user_input: Optional[Dict[str, Any]] = None
    ) -> FlowResult:
        """Manage the options."""
        errors = {}

        if user_input is not None:
            # Validate webhook ID if provided
            webhook_id = user_input.get(CONF_WEBHOOK_ID, "").strip()
            if webhook_id and not webhook_id.replace("_", "").replace("-", "").isalnum():
                errors[CONF_WEBHOOK_ID] = "invalid_webhook_id"
            
            if not errors:
                return self.async_create_entry(title="", data=user_input)

        # Get available light entities
        light_entities = []
        for entity_id in self.hass.states.async_entity_ids("light"):
            state = self.hass.states.get(entity_id)
            if state:
                light_entities.append(entity_id)

        current_config = {**self.config_entry.data, **self.config_entry.options}

        data_schema = vol.Schema(
            {
                vol.Optional(
                    CONF_WEBHOOK_ID,
                    default=current_config.get(CONF_WEBHOOK_ID, "")
                ): cv.string,
                vol.Optional(
                    CONF_LIGHT_ENTITIES,
                    default=current_config.get(CONF_LIGHT_ENTITIES, [])
                ): cv.multi_select({entity: entity for entity in light_entities}),
                vol.Optional(
                    CONF_BRIGHTNESS_CINEMA,
                    default=current_config.get(CONF_BRIGHTNESS_CINEMA, DEFAULT_BRIGHTNESS_CINEMA)
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_BRIGHTNESS_FULLSCREEN,
                    default=current_config.get(CONF_BRIGHTNESS_FULLSCREEN, DEFAULT_BRIGHTNESS_FULLSCREEN)
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_BRIGHTNESS_NORMAL,
                    default=current_config.get(CONF_BRIGHTNESS_NORMAL, DEFAULT_BRIGHTNESS_NORMAL)
                ): vol.All(int, vol.Range(min=1, max=100)),
                vol.Optional(
                    CONF_TRANSITION_TIME,
                    default=current_config.get(CONF_TRANSITION_TIME, DEFAULT_TRANSITION_TIME)
                ): vol.All(int, vol.Range(min=0, max=60)),
            }
        )

        return self.async_show_form(
            step_id="init",
            data_schema=data_schema,
            errors=errors,
        )
